import { useEffect, useState } from 'react';
import { Button } from './ui/button';
import useAgentStore from '../lib/agentStore';
import { LogIn, LogOut } from 'lucide-react';

const QueueSelector = () => {
    const {
        queues,
        selectedQueues,
        queueLoggedIn,
        fetchQueues,
        queueLogin,
        queueLogout
    } = useAgentStore();

    const [loading, setLoading] = useState(false);
    const [selectedQueueIds, setSelectedQueueIds] = useState([]);

    useEffect(() => {
        const loadQueues = async () => {
            try {
                await fetchQueues();
            } catch (error) {
                console.error('Failed to fetch queues:', error);
            }
        };

        loadQueues();
    }, [fetchQueues]);

    useEffect(() => {
        setSelectedQueueIds(selectedQueues);
    }, [selectedQueues]);

    const handleQueueToggle = (queueId) => {
        setSelectedQueueIds((prev) => {
            if (prev.includes(queueId)) {
                return prev.filter((id) => id !== queueId);
            } else {
                return [...prev, queueId];
            }
        });
    };

    const handleQueueLogin = async () => {
        if (selectedQueueIds.length === 0) return;

        setLoading(true);
        try {
            await queueLogin(selectedQueueIds);
        } catch (error) {
            console.error('Failed to login to queues:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleQueueLogout = async () => {
        setLoading(true);
        try {
            await queueLogout();
        } catch (error) {
            console.error('Failed to logout from queues:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="p-4 border rounded-lg shadow-sm">
            <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-medium">Queue Selection</h3>
                {queueLoggedIn && (
                    <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                        Logged In
                    </span>
                )}
            </div>

            <div className="grid grid-cols-2 gap-2 mb-4">
                {queues.map((queue) => (
                    <div key={queue.id} className="flex items-center">
                        <input
                            type="checkbox"
                            id={`queue-${queue.id}`}
                            checked={selectedQueueIds.includes(queue.id)}
                            onChange={() => handleQueueToggle(queue.id)}
                            disabled={queueLoggedIn}
                            className="w-4 h-4 border-gray-300 rounded text-primary focus:ring-primary"
                        />
                        <label
                            htmlFor={`queue-${queue.id}`}
                            className="ml-2 text-sm font-medium text-gray-700"
                        >
                            {queue.name}
                        </label>
                    </div>
                ))}
            </div>

            <div className="flex space-x-2">
                <Button
                    onClick={handleQueueLogin}
                    disabled={queueLoggedIn || selectedQueueIds.length === 0 || loading}
                    className="flex items-center justify-center w-full"
                    size="sm"
                >
                    <LogIn className="w-4 h-4 mr-1" />
                    {loading ? 'Processing...' : 'Login'}
                </Button>

                <Button
                    onClick={handleQueueLogout}
                    disabled={!queueLoggedIn || loading}
                    variant="outline"
                    className="flex items-center justify-center w-full"
                    size="sm"
                >
                    <LogOut className="w-4 h-4 mr-1" />
                    {loading ? 'Processing...' : 'Logout'}
                </Button>
            </div>
        </div>
    );
};

export default QueueSelector;
