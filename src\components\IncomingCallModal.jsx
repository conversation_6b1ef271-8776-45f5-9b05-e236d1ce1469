import { Phone, PhoneOff } from 'lucide-react';
import { Button } from './ui/button';

const IncomingCallModal = ({ phoneNumber, onAccept, onReject, isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-md animate-in fade-in zoom-in rounded-lg bg-white p-6 shadow-lg">
        <div className="mb-4 text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
            <Phone className="h-8 w-8 text-blue-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Incoming Call</h3>
          <p className="mt-1 text-2xl font-bold">{phoneNumber}</p>
        </div>

        <div className="mt-6 grid grid-cols-2 gap-4">
          <Button
            onClick={onReject}
            variant="outline"
            className="flex items-center justify-center border-red-200 bg-red-50 text-red-600 hover:bg-red-100"
          >
            <PhoneOff className="mr-2 h-5 w-5" />
            Reject
          </Button>
          <Button
            onClick={onAccept}
            className="flex items-center justify-center bg-green-600 hover:bg-green-700"
          >
            <Phone className="mr-2 h-5 w-5" />
            Accept
          </Button>
        </div>
      </div>
    </div>
  );
};

export default IncomingCallModal;
