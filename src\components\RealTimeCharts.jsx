import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Clock, 
  Phone,
  PhoneIncoming,
  PhoneOutgoing
} from 'lucide-react';

// Simple chart components since we don't have a charting library
const SimpleBarChart = ({ data, title, color = "blue" }) => {
  const maxValue = Math.max(...data.map(d => d.value));
  
  const colorClasses = {
    blue: "bg-blue-500",
    green: "bg-green-500",
    red: "bg-red-500",
    yellow: "bg-yellow-500",
    purple: "bg-purple-500"
  };

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      <div className="space-y-2">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <span className="text-xs text-gray-600 w-12">{item.label}</span>
            <div className="flex-1 bg-gray-200 rounded-full h-3 relative">
              <div 
                className={`h-3 rounded-full ${colorClasses[color]} transition-all duration-500`}
                style={{ width: `${(item.value / maxValue) * 100}%` }}
              ></div>
            </div>
            <span className="text-xs font-medium text-gray-900 w-8">{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

const SimpleLineChart = ({ data, title, color = "blue" }) => {
  const maxValue = Math.max(...data);
  const minValue = Math.min(...data);
  const range = maxValue - minValue || 1;
  
  const colorClasses = {
    blue: "stroke-blue-500 fill-blue-100",
    green: "stroke-green-500 fill-green-100",
    red: "stroke-red-500 fill-red-100",
    yellow: "stroke-yellow-500 fill-yellow-100",
    purple: "stroke-purple-500 fill-purple-100"
  };

  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * 300;
    const y = 100 - ((value - minValue) / range) * 80;
    return `${x},${y}`;
  }).join(' ');

  const pathD = `M ${points.split(' ').map((point, index) => 
    index === 0 ? `M ${point}` : `L ${point}`
  ).join(' ')}`;

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-700">{title}</h4>
      <div className="bg-gray-50 rounded-lg p-4">
        <svg width="300" height="120" className="w-full h-24">
          <defs>
            <linearGradient id={`gradient-${color}`} x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" className={colorClasses[color].split(' ')[1]} stopOpacity="0.3"/>
              <stop offset="100%" className={colorClasses[color].split(' ')[1]} stopOpacity="0"/>
            </linearGradient>
          </defs>
          <path
            d={`${pathD} L 300,100 L 0,100 Z`}
            fill={`url(#gradient-${color})`}
          />
          <path
            d={pathD}
            fill="none"
            className={colorClasses[color].split(' ')[0]}
            strokeWidth="2"
          />
          {data.map((value, index) => {
            const x = (index / (data.length - 1)) * 300;
            const y = 100 - ((value - minValue) / range) * 80;
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="3"
                className={colorClasses[color].split(' ')[0]}
                fill="white"
                strokeWidth="2"
              />
            );
          })}
        </svg>
      </div>
    </div>
  );
};

const CallVolumeChart = ({ metrics }) => {
  const [timeData, setTimeData] = useState([]);

  useEffect(() => {
    // Generate mock time-series data for the last 24 hours
    const hours = Array.from({ length: 24 }, (_, i) => {
      const hour = new Date();
      hour.setHours(i, 0, 0, 0);
      return {
        time: hour.toLocaleTimeString('en-US', { hour: '2-digit', hour12: false }),
        inbound: Math.floor(Math.random() * 50) + 10,
        outbound: Math.floor(Math.random() * 30) + 5,
        total: 0
      };
    });
    
    hours.forEach(h => h.total = h.inbound + h.outbound);
    setTimeData(hours);
  }, []);

  const currentHourData = [
    { label: 'Inbound', value: metrics.inboundCallsToday || 0 },
    { label: 'Outbound', value: metrics.outboundCallsToday || 0 },
    { label: 'Total', value: (metrics.inboundCallsToday || 0) + (metrics.outboundCallsToday || 0) }
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <SimpleBarChart 
        data={currentHourData}
        title="Today's Call Volume"
        color="blue"
      />
      <SimpleLineChart 
        data={timeData.slice(-12).map(d => d.total)}
        title="Call Trend (Last 12 Hours)"
        color="green"
      />
    </div>
  );
};

const AgentPerformanceChart = ({ metrics }) => {
  const performanceData = [
    { label: 'Available', value: metrics.availableAgents || 0 },
    { label: 'On Call', value: metrics.busyAgents || 0 },
    { label: 'Break', value: metrics.onBreakAgents || 0 },
    { label: 'Not Ready', value: Math.max(0, (metrics.totalActiveAgents || 0) - (metrics.availableAgents || 0) - (metrics.busyAgents || 0) - (metrics.onBreakAgents || 0)) }
  ];

  const serviceLevelTrend = Array.from({ length: 12 }, () => 
    85 + Math.floor(Math.random() * 15)
  );

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <SimpleBarChart 
        data={performanceData}
        title="Agent Status Distribution"
        color="purple"
      />
      <SimpleLineChart 
        data={serviceLevelTrend}
        title="Service Level Trend (%)"
        color="green"
      />
    </div>
  );
};

const RealTimeCharts = ({ metrics, selectedQueue, isLoading }) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Real-time Analytics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Real-time Analytics</span>
          </CardTitle>
          <Badge variant="outline" className="text-xs">
            {selectedQueue === 'all' ? 'All Queues' : selectedQueue}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="calls" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="calls" className="flex items-center space-x-2">
              <Phone className="h-4 w-4" />
              <span>Call Volume</span>
            </TabsTrigger>
            <TabsTrigger value="performance" className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Performance</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="calls" className="mt-6">
            <CallVolumeChart metrics={metrics} />
          </TabsContent>
          
          <TabsContent value="performance" className="mt-6">
            <AgentPerformanceChart metrics={metrics} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default RealTimeCharts;
