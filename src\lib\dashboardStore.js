import { create } from 'zustand';


const generateMockAgents = () => {
    const statuses = ['Available', 'On Call', 'Wrap-up', 'Break', 'Not Ready'];
    const names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    const queues = ['Sales', 'Support', 'Billing', 'Technical'];

    return Array.from({ length: 15 }, (_, i) => ({
        id: `agent-${i + 1}`,
        name: names[i % names.length],
        extension: `100${i + 1}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        currentCallDuration: Math.floor(Math.random() * 1800),
        callsToday: Math.floor(Math.random() * 50),
        avgHandleTime: 180 + Math.floor(Math.random() * 300),
        queues: [queues[Math.floor(Math.random() * queues.length)]],
        lastActivity: new Date(Date.now() - Math.floor(Math.random() * 3600000)),
    }));
};

const generateMockCalls = () => {
    const types = ['Inbound', 'Outbound'];
    const queues = ['Sales', 'Support', 'Billing', 'Technical'];
    const dispositions = ['Connected', 'Ringing', 'Hold', 'Transferring'];

    return Array.from({ length: 8 }, (_, i) => ({
        id: `call-${i + 1}`,
        type: types[Math.floor(Math.random() * types.length)],
        phoneNumber: `******-${String(Math.floor(Math.random() * 9000) + 1000)}`,
        agentId: `agent-${Math.floor(Math.random() * 15) + 1}`,
        queue: queues[Math.floor(Math.random() * queues.length)],
        startTime: new Date(Date.now() - Math.floor(Math.random() * 1800000)),
        duration: Math.floor(Math.random() * 1800),
        status: dispositions[Math.floor(Math.random() * dispositions.length)],
        customerName: `Customer ${i + 1}`,
    }));
};

const generateMockQueues = () => [
    {
        id: 'sales',
        name: 'Sales',
        waitingCalls: Math.floor(Math.random() * 10),
        longestWait: Math.floor(Math.random() * 600),
        avgWaitTime: Math.floor(Math.random() * 300),
        serviceLevel: 85 + Math.floor(Math.random() * 15),
        abandonedCalls: Math.floor(Math.random() * 5),
    },
    {
        id: 'support',
        name: 'Support',
        waitingCalls: Math.floor(Math.random() * 15),
        longestWait: Math.floor(Math.random() * 900),
        avgWaitTime: Math.floor(Math.random() * 400),
        serviceLevel: 80 + Math.floor(Math.random() * 20),
        abandonedCalls: Math.floor(Math.random() * 8),
    },
    {
        id: 'billing',
        name: 'Billing',
        waitingCalls: Math.floor(Math.random() * 8),
        longestWait: Math.floor(Math.random() * 450),
        avgWaitTime: Math.floor(Math.random() * 250),
        serviceLevel: 90 + Math.floor(Math.random() * 10),
        abandonedCalls: Math.floor(Math.random() * 3),
    },
    {
        id: 'technical',
        name: 'Technical',
        waitingCalls: Math.floor(Math.random() * 12),
        longestWait: Math.floor(Math.random() * 1200),
        avgWaitTime: Math.floor(Math.random() * 600),
        serviceLevel: 75 + Math.floor(Math.random() * 25),
        abandonedCalls: Math.floor(Math.random() * 6),
    },
];

const generateMockMetrics = () => ({
    totalActiveAgents: 15,
    availableAgents: 6 + Math.floor(Math.random() * 4),
    busyAgents: 5 + Math.floor(Math.random() * 5),
    onBreakAgents: 2 + Math.floor(Math.random() * 3),
    totalCallsInProgress: 8 + Math.floor(Math.random() * 7),
    totalCallsToday: 245 + Math.floor(Math.random() * 100),
    inboundCallsToday: 180 + Math.floor(Math.random() * 50),
    outboundCallsToday: 65 + Math.floor(Math.random() * 30),
    avgWaitTime: 45 + Math.floor(Math.random() * 60),
    avgHandleTime: 240 + Math.floor(Math.random() * 120),
    serviceLevel: 87 + Math.floor(Math.random() * 10),
    abandonedCalls: 12 + Math.floor(Math.random() * 8),
    callsInQueue: 15 + Math.floor(Math.random() * 10),
    longestWaitTime: 120 + Math.floor(Math.random() * 300),
    inboundConnectRate: 92 + Math.floor(Math.random() * 8),
    outboundConnectRate: 68 + Math.floor(Math.random() * 15),
    firstCallResolution: 78 + Math.floor(Math.random() * 15),
});

const useDashboardStore = create((set, get) => ({
    metrics: {},
    agents: [],
    calls: [],
    queues: [],
    lastUpdated: null,
    initializeMockData: () => {
        set({
            metrics: generateMockMetrics(),
            agents: generateMockAgents(),
            calls: generateMockCalls(),
            queues: generateMockQueues(),
            lastUpdated: new Date(),
        });
    },

    fetchDashboardData: async (selectedQueue = 'all') => {
        try {
            set({
                metrics: generateMockMetrics(),
                agents: generateMockAgents(),
                calls: generateMockCalls(),
                queues: generateMockQueues(),
                lastUpdated: new Date(),
            });
        } catch (error) {
            console.error('Failed to fetch dashboard data:', error);
        }
    },

    updateMetrics: (newMetrics) => {
        set(state => ({
            metrics: { ...state.metrics, ...newMetrics },
            lastUpdated: new Date(),
        }));
    },

    updateAgentStatus: (agentId, status) => {
        set(state => ({
            agents: state.agents.map(agent =>
                agent.id === agentId ? { ...agent, status } : agent
            ),
            lastUpdated: new Date(),
        }));
    },

    addCall: (call) => {
        set(state => ({
            calls: [call, ...state.calls],
            lastUpdated: new Date(),
        }));
    },

    removeCall: (callId) => {
        set(state => ({
            calls: state.calls.filter(call => call.id !== callId),
            lastUpdated: new Date(),
        }));
    },

    updateCall: (callId, updates) => {
        set(state => ({
            calls: state.calls.map(call =>
                call.id === callId ? { ...call, ...updates } : call
            ),
            lastUpdated: new Date(),
        }));
    },
}));

export default useDashboardStore;
