import { Routes, Route, Navigate } from 'react-router-dom';
import LiveDashboardPage from './pages/LiveDashboardPage';
import TestDashboard from './pages/TestDashboard';

function App() {
    return (
        <Routes>
            <Route path="/dashboard" element={<LiveDashboardPage />} />
            <Route path="/test" element={<TestDashboard />} />
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
        </Routes>
    );
}

export default App;
