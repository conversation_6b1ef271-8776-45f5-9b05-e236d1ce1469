import { useEffect } from 'react';
import { Phone, PhoneOutgoing } from 'lucide-react';
import useAgentStore from '../lib/agentStore';

const CallHistoryTable = () => {
  const { callHistory, fetchCallHistory } = useAgentStore();
  
  useEffect(() => {
    const loadCallHistory = async () => {
      try {
        await fetchCallHistory();
      } catch (error) {
        console.error('Failed to fetch call history:', error);
      }
    };
    
    loadCallHistory();
  }, [fetchCallHistory]);
  
  // Format duration as MM:SS
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Format date as HH:MM
  const formatTime = (date) => {
    if (!date) return '--';
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  return (
    <div className="rounded-lg border p-4 shadow-sm">
      <h3 className="mb-4 text-lg font-medium">Recent Call History</h3>
      
      <div className="overflow-hidden rounded-md border">
        <table className="w-full text-sm">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-2 text-left font-medium text-gray-500">Type</th>
              <th className="px-4 py-2 text-left font-medium text-gray-500">Number</th>
              <th className="px-4 py-2 text-left font-medium text-gray-500">Time</th>
              <th className="px-4 py-2 text-left font-medium text-gray-500">Duration</th>
              <th className="px-4 py-2 text-left font-medium text-gray-500">Disposition</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {callHistory.slice(0, 5).map((call) => (
              <tr key={call.id} className="hover:bg-gray-50">
                <td className="px-4 py-2">
                  <div className="flex items-center">
                    {call.type === 'inbound' ? (
                      <Phone className="mr-1 h-4 w-4 text-blue-500" />
                    ) : (
                      <PhoneOutgoing className="mr-1 h-4 w-4 text-green-500" />
                    )}
                    <span className="capitalize">{call.type}</span>
                  </div>
                </td>
                <td className="px-4 py-2">{call.phoneNumber}</td>
                <td className="px-4 py-2">{formatTime(call.startTime)}</td>
                <td className="px-4 py-2">{call.duration ? formatDuration(call.duration) : '--'}</td>
                <td className="px-4 py-2">
                  {call.disposition ? (
                    <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                      {call.disposition}
                    </span>
                  ) : (
                    <span className="text-gray-400">--</span>
                  )}
                </td>
              </tr>
            ))}
            
            {callHistory.length === 0 && (
              <tr>
                <td colSpan="5" className="px-4 py-8 text-center text-gray-500">
                  No call history available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CallHistoryTable;
