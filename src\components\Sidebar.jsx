import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Button } from './ui/button';
import { Avatar, AvatarFallback } from './ui/avatar';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from './ui/dropdown-menu';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from './ui/tooltip';
import {
    Home,
    Settings,
    LogOut,
    User,
    BarChart3
} from 'lucide-react';
import useAgentStore from '../lib/agentStore';

const Sidebar = () => {
    const { currentUser, logout } = useAuth();
    const navigate = useNavigate();
    const { status } = useAgentStore();

    const handleLogout = async () => {
        try {
            await logout();
            navigate('/login');
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    const getInitial = () => {
        if (!currentUser) return '?';

        const name = currentUser.name || currentUser.username || '';
        return name.charAt(0).toUpperCase();
    };

    const getStatusColor = () => {
        switch (status) {
            case 'READY':
                return 'ring-green-500';
            case 'NOT_READY':
                return 'ring-yellow-500';
            case 'ON_CALL':
                return 'ring-blue-500';
            case 'BREAK':
                return 'ring-purple-500';
            default:
                return 'ring-gray-300';
        }
    };

    return (
        <div className="h-full bg-white border-r flex flex-col w-16 overflow-hidden">
            <div className="h-16 border-b flex justify-center items-center">
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <div>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" className="p-0 h-auto w-auto">
                                            <Avatar className={`h-10 w-10 ring-2 ${getStatusColor()}`}>
                                                <AvatarFallback className="bg-primary text-primary-foreground">
                                                    {getInitial()}
                                                </AvatarFallback>
                                            </Avatar>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="start" className="w-56">
                                        <DropdownMenuLabel>My Account</DropdownMenuLabel>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem className="cursor-pointer">
                                            <User className="mr-2 h-4 w-4" />
                                            <span>Profile</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem className="cursor-pointer">
                                            <Settings className="mr-2 h-4 w-4" />
                                            <span>Settings</span>
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem className="cursor-pointer" onClick={handleLogout}>
                                            <LogOut className="mr-2 h-4 w-4" />
                                            <span>Logout</span>
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                            <p>Account Menu</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>

            <nav className="flex-1 p-2">
                <ul className="space-y-6 mt-6">
                    <li className="flex justify-center">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        className="w-12 h-12 rounded-full p-0 flex items-center justify-center"
                                        onClick={() => navigate('/')}
                                    >
                                        <Home className="h-6 w-6" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent side="right">
                                    <p>Dashboard</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </li>
                    <li className="flex justify-center">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        className="w-12 h-12 rounded-full p-0 flex items-center justify-center"
                                        onClick={() => navigate('/performance')}
                                    >
                                        <BarChart3 className="h-6 w-6" />
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent side="right">
                                    <p>My Dashboard</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </li>
                </ul>
            </nav>

            <div className="p-4 border-t mt-auto flex justify-center">
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                variant="ghost"
                                className="w-12 h-12 rounded-full p-0 flex items-center justify-center text-red-500 hover:text-red-600 hover:bg-red-50"
                                onClick={handleLogout}
                            >
                                <LogOut className="h-6 w-6" />
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent side="right">
                            <p>Logout</p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        </div>
    );
};

export default Sidebar;
