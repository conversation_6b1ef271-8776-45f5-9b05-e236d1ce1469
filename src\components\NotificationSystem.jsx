import { useState, useEffect, useRef } from 'react';
import { X, Bell, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { Button } from './ui/button';
import { Toaster } from './ui/toaster';
import { useToast } from './ui/use-toast';

// Notification types
const NOTIFICATION_TYPES = {
    INCOMING_CALL: 'incoming_call',
    MISSED_CALL: 'missed_call',
    CALLBACK_DUE: 'callback_due',
    SYSTEM: 'system',
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
};

// Notification component for the notification center
const NotificationItem = ({ notification, onDismiss }) => {
    const getIcon = () => {
        switch (notification.type) {
            case NOTIFICATION_TYPES.INCOMING_CALL:
                return <Bell className="w-5 h-5 text-blue-500" />;
            case NOTIFICATION_TYPES.MISSED_CALL:
                return <Bell className="w-5 h-5 text-red-500" />;
            case NOTIFICATION_TYPES.CALLBACK_DUE:
                return <Bell className="w-5 h-5 text-purple-500" />;
            case NOTIFICATION_TYPES.SUCCESS:
                return <CheckCircle className="w-5 h-5 text-green-500" />;
            case NOTIFICATION_TYPES.ERROR:
                return <AlertCircle className="w-5 h-5 text-red-500" />;
            case NOTIFICATION_TYPES.WARNING:
                return <AlertCircle className="w-5 h-5 text-yellow-500" />;
            default:
                return <Info className="w-5 h-5 text-blue-500" />;
        }
    };

    const getTimeAgo = (timestamp) => {
        const seconds = Math.floor((new Date() - new Date(timestamp)) / 1000);

        if (seconds < 60) return `${seconds}s ago`;

        const minutes = Math.floor(seconds / 60);
        if (minutes < 60) return `${minutes}m ago`;

        const hours = Math.floor(minutes / 60);
        if (hours < 24) return `${hours}h ago`;

        const days = Math.floor(hours / 24);
        return `${days}d ago`;
    };

    return (
        <div className="flex items-start p-3 border-b border-gray-100 hover:bg-gray-50">
            <div className="flex-shrink-0 mr-3">{getIcon()}</div>
            <div className="flex-1 min-w-0">
                <p className="text-sm font-medium">{notification.title}</p>
                <p className="text-xs text-gray-500 mt-0.5">{notification.message}</p>
                <p className="mt-1 text-xs text-gray-400">{getTimeAgo(notification.timestamp)}</p>
            </div>
            <Button
                variant="ghost"
                size="sm"
                className="w-6 h-6 p-0 ml-2"
                onClick={() => onDismiss(notification.id)}
            >
                <X className="w-4 h-4" />
            </Button>
        </div>
    );
};

// Notification Center
const NotificationCenter = ({ notifications, onDismiss, onClearAll }) => {
    if (notifications.length === 0) {
        return (
            <div className="p-4 text-sm text-center text-gray-500">
                No notifications
            </div>
        );
    }

    return (
        <div className="max-h-[400px] overflow-y-auto">
            <div className="flex items-center justify-between p-3 border-b">
                <h3 className="text-sm font-medium">Notifications</h3>
                <Button variant="ghost" size="sm" className="h-8 text-xs" onClick={onClearAll}>
                    Clear All
                </Button>
            </div>
            {notifications.map((notification) => (
                <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onDismiss={onDismiss}
                />
            ))}
        </div>
    );
};

// Main notification system component
const NotificationSystem = () => {
    const [notifications, setNotifications] = useState([]);
    const [showNotificationCenter, setShowNotificationCenter] = useState(false);
    const notificationCenterRef = useRef(null);
    const { toast } = useToast();

    // Add a notification
    const addNotification = (type, title, message, options = {}) => {
        const newNotification = {
            id: Date.now().toString(),
            type,
            title,
            message,
            timestamp: new Date(),
            ...options
        };

        setNotifications(prev => [newNotification, ...prev]);

        // Show toast for important notifications
        if (type !== NOTIFICATION_TYPES.SYSTEM) {
            toast({
                title,
                description: message,
                variant: type === NOTIFICATION_TYPES.ERROR ? 'destructive' : 'default',
            });
        }

        return newNotification.id;
    };

    // Dismiss a notification
    const dismissNotification = (id) => {
        setNotifications(prev => prev.filter(notification => notification.id !== id));
    };

    // Clear all notifications
    const clearAllNotifications = () => {
        setNotifications([]);
    };

    // Close notification center when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (
                notificationCenterRef.current &&
                !notificationCenterRef.current.contains(event.target) &&
                !event.target.closest('[data-notification-toggle]')
            ) {
                setShowNotificationCenter(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Expose methods to the window for global access
    useEffect(() => {
        window.notificationSystem = {
            addNotification,
            dismissNotification,
            clearAllNotifications
        };

        return () => {
            delete window.notificationSystem;
        };
    }, []);

    return (
        <>
            <Toaster />

            <div className="relative">
                <Button
                    variant="ghost"
                    size="sm"
                    className="relative w-10 h-10 p-0 rounded-full"
                    onClick={() => setShowNotificationCenter(!showNotificationCenter)}
                    data-notification-toggle
                >
                    <Bell className="w-5 h-5" />
                    {notifications.length > 0 && (
                        <span className="absolute top-1 right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                            {notifications.length > 9 ? '9+' : notifications.length}
                        </span>
                    )}
                </Button>

                {showNotificationCenter && (
                    <div
                        ref={notificationCenterRef}
                        className="absolute right-0 z-50 bg-white border rounded-md shadow-lg top-12 w-80"
                    >
                        <NotificationCenter
                            notifications={notifications}
                            onDismiss={dismissNotification}
                            onClearAll={clearAllNotifications}
                        />
                    </div>
                )}
            </div>
        </>
    );
};

export default NotificationSystem;
export { NOTIFICATION_TYPES };
