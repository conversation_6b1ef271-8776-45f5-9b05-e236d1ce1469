import { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import useAgentStore from '../lib/agentStore';
import sipService from '../lib/sipService';

const StatusControls = () => {
    const {
        status,
        statusReason,
        setReady,
        setNotReady,
        breakCodes,
        setBreak,
        fetchBreakCodes,
        checkAgentReady
    } = useAgentStore();
    const [showBreakMenu, setShowBreakMenu] = useState(false);

    useEffect(() => {
        const initializeComponent = async () => {
            try {
                await fetchBreakCodes();

                await checkAgentReady();
            } catch (error) {
                console.error('Failed to initialize status controls:', error);
            }
        };

        initializeComponent();
    }, [fetchBreakCodes, checkAgentReady]);

    const getStatusColor = () => {
        switch (status) {
            case 'READY':
                return 'bg-green-500';
            case 'NOT_READY':
                return 'bg-yellow-500';
            case 'ON_CALL':
                return 'bg-blue-500';
            case 'BREAK':
                return 'bg-purple-500';
            default:
                return 'bg-gray-500';
        }
    };

    const handleReadyClick = async () => {
        try {
            console.log('Setting agent to READY state and registering with SIP server...');

            await setReady();

            if (!sipService.initialized) {
                console.log('SIP service not initialized, initializing now...');
                await sipService.initialize();
            }

            console.log('Registering with SIP server...');
            const registered = await sipService.register();

            if (registered) {
                console.log('SIP registration successful');
            } else {
                console.error('SIP registration failed');
                alert('Failed to register with SIP server. You may not receive calls.');
            }
        } catch (error) {
            console.error('Failed to set status to READY:', error);
            alert('Failed to set status to READY: ' + (error?.message || 'Unknown error'));
        }
    };

    const handleNotReadyClick = async () => {
        try {
            console.log('Setting agent to NOT_READY state and unregistering from SIP server...');

            await setNotReady('Agent initiated');

            if (!sipService.initialized) {
                console.log('SIP service not initialized, no need to unregister');
                return;
            }

            console.log('Unregistering from SIP server...');
            const unregistered = await sipService.unregister();

            if (unregistered) {
                console.log('SIP unregistration successful');
            } else {
                console.error('SIP unregistration failed');
                console.warn('Failed to unregister from SIP server. You may still receive calls.');
            }
        } catch (error) {
            console.error('Failed to set status to NOT_READY:', error);
        }
    };

    const handleBreakClick = () => {
        setShowBreakMenu(!showBreakMenu);
    };

    const handleBreakSelect = async (breakCode) => {
        try {

            await setBreak(breakCode);
            if (!sipService.initialized) {
                console.log('SIP service not initialized, no need to unregister');
                setShowBreakMenu(false);
                return;
            }

            console.log('Unregistering from SIP server...');
            const unregistered = await sipService.unregister();

            if (unregistered) {
                console.log('SIP unregistration successful');
            } else {
                console.error('SIP unregistration failed');
                console.warn('Failed to unregister from SIP server. You may still receive calls.');
            }

            setShowBreakMenu(false);
        } catch (error) {
            console.error('Failed to set break:', error);
            setShowBreakMenu(false);
        }
    };

    return (
        <div className="flex flex-col w-full">
            <div className="flex items-center mb-3">
                <div className={`mr-2 h-3 w-3 rounded-full ${getStatusColor()}`}></div>
                <span className="text-sm font-medium">
                    {status}
                    {statusReason && ` (${statusReason})`}
                </span>
            </div>

            <div className="flex flex-col w-full space-y-2">
                <Button
                    onClick={handleReadyClick}
                    variant={status === 'READY' ? 'default' : 'outline'}
                    className={`w-full justify-start ${status === 'READY' ? 'bg-green-600 hover:bg-green-700' : ''}`}
                    disabled={status === 'ON_CALL'}
                    size="sm"
                >
                    Ready
                </Button>

                <Button
                    onClick={handleNotReadyClick}
                    variant={status === 'NOT_READY' ? 'default' : 'outline'}
                    className={`w-full justify-start ${status === 'NOT_READY' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}`}
                    disabled={status === 'ON_CALL'}
                    size="sm"
                >
                    Not Ready
                </Button>

                <div className="relative w-full">
                    <Button
                        onClick={handleBreakClick}
                        variant={status === 'BREAK' ? 'default' : 'outline'}
                        className={`w-full justify-start ${status === 'BREAK' ? 'bg-purple-600 hover:bg-purple-700' : ''}`}
                        disabled={status === 'ON_CALL'}
                        size="sm"
                    >
                        Break
                    </Button>

                    {showBreakMenu && (
                        <div className="absolute left-0 z-10 w-full py-1 mt-2 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
                            {breakCodes.map((breakCode) => (
                                <button
                                    key={breakCode.id}
                                    className="flex items-center w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100"
                                    onClick={() => handleBreakSelect(breakCode.id)}
                                >
                                    <span>{breakCode.name}</span>
                                </button>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default StatusControls;
