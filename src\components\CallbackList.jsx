import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Phone, Calendar, Clock, AlertCircle, ChevronDown, ChevronUp } from 'lucide-react';
import useAgentStore from '../lib/agentStore';
import sipService from '../lib/sipService';

const CallbackList = () => {
    const { callbacks, removeCallback } = useAgentStore();
    const [expanded, setExpanded] = useState(true);
    const [loading, setLoading] = useState(false);
    const [activeCallbackId, setActiveCallbackId] = useState(null);

    // Sort callbacks by scheduled time
    const sortedCallbacks = [...callbacks].sort((a, b) =>
        new Date(a.scheduledTime) - new Date(b.scheduledTime)
    );

    const formatTime = (date) => {
        if (!date) return '--';
        return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    const formatDate = (date) => {
        if (!date) return '--';
        return new Date(date).toLocaleDateString();
    };

    const isToday = (date) => {
        if (!date) return false;
        const today = new Date();
        const callbackDate = new Date(date);
        return (
            callbackDate.getDate() === today.getDate() &&
            callbackDate.getMonth() === today.getMonth() &&
            callbackDate.getFullYear() === today.getFullYear()
        );
    };

    const isOverdue = (date) => {
        if (!date) return false;
        return new Date(date) < new Date();
    };

    const handleCall = async (callback) => {
        if (loading) return;

        setLoading(true);
        setActiveCallbackId(callback.id);

        try {
            // Make the call
            const success = await sipService.makeCall(callback.phoneNumber);

            if (success) {
                await removeCallback(callback.id);
            }
        } catch (error) {
            console.error('Failed to make callback call:', error);
        } finally {
            setLoading(false);
            setActiveCallbackId(null);
        }
    };

    if (callbacks.length === 0) {
        return (
            <Card className="shadow-sm">
                <CardHeader className="px-4 py-3">
                    <CardTitle className="text-sm font-medium">Scheduled Callbacks</CardTitle>
                </CardHeader>
                <CardContent className="px-4 pb-3 text-sm text-center text-gray-500">
                    No callbacks scheduled
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between px-4 py-3">
                <CardTitle className="text-sm font-medium">
                    Scheduled Callbacks
                    <Badge variant="secondary" className="ml-2">
                        {callbacks.length}
                    </Badge>
                </CardTitle>
                <Button variant="ghost" size="sm" onClick={() => setExpanded(!expanded)} className="w-8 h-8 p-0">
                    {expanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                </Button>
            </CardHeader>

            {expanded && (
                <CardContent className="px-4 pb-3">
                    <div className="space-y-2">
                        {sortedCallbacks.map((callback) => (
                            <div
                                key={callback.id}
                                className={`rounded-md border p-3 ${isOverdue(callback.scheduledTime)
                                        ? 'border-red-200 bg-red-50'
                                        : isToday(callback.scheduledTime)
                                            ? 'border-blue-200 bg-blue-50'
                                            : 'border-gray-200'
                                    }`}
                            >
                                <div className="flex items-center justify-between mb-2">
                                    <div className="flex items-center">
                                        <Phone className="h-4 w-4 mr-1.5 text-gray-500" />
                                        <span className="font-medium">{callback.phoneNumber}</span>

                                        {isOverdue(callback.scheduledTime) && (
                                            <Badge variant="destructive" className="ml-2 text-xs">Overdue</Badge>
                                        )}

                                        {isToday(callback.scheduledTime) && !isOverdue(callback.scheduledTime) && (
                                            <Badge variant="secondary" className="ml-2 text-xs text-blue-800 bg-blue-100 hover:bg-blue-100">Today</Badge>
                                        )}
                                    </div>

                                    <Button
                                        size="sm"
                                        className="h-8 text-xs"
                                        onClick={() => handleCall(callback)}
                                        disabled={loading && activeCallbackId === callback.id}
                                    >
                                        {loading && activeCallbackId === callback.id ? 'Calling...' : 'Call Now'}
                                    </Button>
                                </div>

                                <div className="grid grid-cols-2 gap-2 mb-2 text-xs">
                                    <div className="flex items-center">
                                        <Calendar className="h-3.5 w-3.5 mr-1 text-gray-500" />
                                        <span>{formatDate(callback.scheduledTime)}</span>
                                    </div>
                                    <div className="flex items-center">
                                        <Clock className="h-3.5 w-3.5 mr-1 text-gray-500" />
                                        <span>{formatTime(callback.scheduledTime)}</span>
                                    </div>
                                </div>

                                {callback.reason && (
                                    <div className="mb-1 text-xs text-gray-600">
                                        <span className="font-medium">Reason:</span> {callback.reason}
                                    </div>
                                )}

                                {callback.notes && (
                                    <div className="text-xs text-gray-600">
                                        <span className="font-medium">Notes:</span> {callback.notes}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </CardContent>
            )}
        </Card>
    );
};

export default CallbackList;
