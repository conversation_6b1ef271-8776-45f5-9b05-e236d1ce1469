import { create } from 'zustand';
import { queueApi } from '../api/apiClient';

const mockWorkCodes = [
    { id: '1', name: 'Sale Completed' },
    { id: '2', name: 'Information Provided' },
    { id: '3', name: 'Callback Scheduled' },
    { id: '4', name: 'Issue Resolved' },
    { id: '5', name: 'Escalated to Manager' }
];

const useAgentStore = create((set, get) => ({

    status: 'OFFLINE',
    statusReason: null,
    statusDuration: 0,
    queueLoggedIn: false,
    queues: [],
    selectedQueues: [],
    workCodes: mockWorkCodes,
    breakCodes: [],
    callData: null,
    callHistory: [],
    callbacks: [],
    missedCalls: [],
    notifications: [],

    setStatus: async (status, reason = null) => {
        try {
            await new Promise(resolve => setTimeout(resolve, 300));
            set({ status, statusReason: reason });
            return { success: true };
        } catch (error) {
            console.error('Failed to set agent status:', error);
            throw error;
        }
    },

    setReady: async () => {
        try {
            const response = await queueApi.unpause();

            if (response && response.success) {
                set({ status: 'READY', statusReason: null });
                return { success: true };
            } else {
                throw new Error('Failed to set agent to READY state');
            }
        } catch (error) {
            console.error('Failed to set agent to READY state:', error);
            throw error;
        }
    },

    setNotReady: async (reason = null) => {
        set({ status: 'NOT_READY', statusReason: reason });
        return { success: true };
    },

    setBreak: async (breakCode) => {
        try {
            const response = await queueApi.pause(breakCode);

            if (response && response.success) {
                const breakCodeObj = get().breakCodes.find(code => code.id === breakCode);
                const breakCodeName = breakCodeObj ? breakCodeObj.name : breakCode;

                set({ status: 'BREAK', statusReason: breakCodeName });
                return { success: true };
            } else {
                throw new Error('Failed to set agent to BREAK state');
            }
        } catch (error) {
            console.error('Failed to set agent break:', error);
            throw error;
        }
    },

    queueLogin: async () => {
        try {
            const response = await queueApi.login();

            if (response && response.success) {
                set({
                    queueLoggedIn: true,
                    selectedQueues: queueIds
                });
                return { success: true };
            } else {
                throw new Error('Failed to login to queues');
            }
        } catch (error) {
            console.error('Failed to login to queues:', error);
            throw error;
        }
    },

    queueLogout: async () => {
        try {
            const response = await queueApi.logout();

            if (response && response.success) {
                set({
                    queueLoggedIn: false,
                    selectedQueues: []
                });
                return { success: true };
            } else {
                throw new Error('Failed to logout from queues');
            }
        } catch (error) {
            console.error('Failed to logout from queues:', error);
            throw error;
        }
    },

    checkQueueLogin: async () => {
        try {
            const response = await queueApi.isLogin();

            if (response !== undefined) {
                set({ queueLoggedIn: !!response });
                return !!response;
            }
            return false;
        } catch (error) {
            console.error('Failed to check queue login status:', error);
            return false;
        }
    },

    checkAgentReady: async () => {
        try {
            const response = await queueApi.isReady();

            if (response !== undefined) {
                if (response) {
                    set({ status: 'READY', statusReason: null });
                }
                return !!response;
            }
            return false;
        } catch (error) {
            console.error('Failed to check agent ready status:', error);
            return false;
        }
    },

    submitWorkCode: async (callId, workCode, notes = '') => {
        try {
            await new Promise(resolve => setTimeout(resolve, 700));

            const { workCodes, callHistory } = get();
            const workCodeObj = workCodes.find(code => code.id === workCode);
            const workCodeName = workCodeObj ? workCodeObj.name : 'Unknown';

            const updatedHistory = callHistory.map(call => {
                if (call.id === callId) {
                    return {
                        ...call,
                        disposition: workCodeName,
                        notes: notes
                    };
                }
                return call;
            });

            set({ callHistory: updatedHistory });

            return { success: true };
        } catch (error) {
            console.error('Failed to submit work code:', error);
            throw error;
        }
    },

    fetchQueues: async () => {
        try {
            const response = await queueApi.getQueues();
            if (response) {
                set({ queues: response });
                return response;
            }
            return [];
        } catch (error) {
            console.error('Failed to fetch queues:', error);
            throw error;
        }
    },

    fetchWorkCodes: async () => {
        try {
            await new Promise(resolve => setTimeout(resolve, 300));
            set({ workCodes: mockWorkCodes });
            return mockWorkCodes;
        } catch (error) {
            console.error('Failed to fetch work codes:', error);
            throw error;
        }
    },

    fetchBreakCodes: async () => {
        try {
            const response = await queueApi.getPauseReasons();
            if (response) {
                set({ breakCodes: response });
                return response;
            }
            return [];
        } catch (error) {
            console.error('Failed to fetch break codes:', error);
            throw error;
        }
    },

    setCallData: (callData) => {
        set({ callData, status: 'ON_CALL' });

        if (callData && !get().callHistory.find(call => call.id === callData.id)) {
            const enhancedCallData = {
                ...callData,
                endTime: null,
                duration: 0,
                disposition: null
            };

            set(state => ({
                callHistory: [enhancedCallData, ...state.callHistory].slice(0, 50)
            }));
        }
    },

    clearCallData: () => {
        const { callData, callHistory } = get();

        if (callData) {
            const updatedHistory = callHistory.map(call => {
                if (call.id === callData.id) {
                    const endTime = new Date();
                    const duration = Math.floor((endTime - new Date(call.startTime)) / 1000);
                    return {
                        ...call,
                        endTime,
                        duration
                    };
                }
                return call;
            });

            set({
                callData: null,
                callHistory: updatedHistory
            });
        } else {
            set({ callData: null });
        }
    },

    scheduleCallback: async (callbackData) => {
        try {
            await new Promise(resolve => setTimeout(resolve, 500));

            set(state => ({
                callbacks: [callbackData, ...state.callbacks]
            }));

            const notification = {
                id: Date.now().toString(),
                type: 'callback_scheduled',
                title: 'Callback Scheduled',
                message: `Callback scheduled for ${callbackData.phoneNumber} at ${new Date(callbackData.scheduledTime).toLocaleString()}`,
                timestamp: new Date()
            };

            set(state => ({
                notifications: [notification, ...state.notifications]
            }));

            return { success: true, callbackId: callbackData.id };
        } catch (error) {
            console.error('Failed to schedule callback:', error);
            throw error;
        }
    },

    removeCallback: async (callbackId) => {
        try {
            await new Promise(resolve => setTimeout(resolve, 300));

            set(state => ({
                callbacks: state.callbacks.filter(callback => callback.id !== callbackId)
            }));

            return { success: true };
        } catch (error) {
            console.error('Failed to remove callback:', error);
            throw error;
        }
    },

    addMissedCall: async (missedCallData) => {
        try {
            await new Promise(resolve => setTimeout(resolve, 300));

            const missedCall = {
                id: missedCallData.id || Date.now().toString(),
                phoneNumber: missedCallData.phoneNumber,
                time: missedCallData.time || new Date(),
                reason: missedCallData.reason || 'Call not answered',
                ...missedCallData
            };

            set(state => ({
                missedCalls: [missedCall, ...state.missedCalls]
            }));

            const notification = {
                id: Date.now().toString(),
                type: 'missed_call',
                title: 'Missed Call',
                message: `Missed call from ${missedCall.phoneNumber}`,
                timestamp: new Date()
            };

            set(state => ({
                notifications: [notification, ...state.notifications]
            }));

            return { success: true, missedCallId: missedCall.id };
        } catch (error) {
            console.error('Failed to add missed call:', error);
            throw error;
        }
    },

    removeMissedCall: async (missedCallId) => {
        try {
            await new Promise(resolve => setTimeout(resolve, 300));

            set(state => ({
                missedCalls: state.missedCalls.filter(call => call.id !== missedCallId)
            }));

            return { success: true };
        } catch (error) {
            console.error('Failed to remove missed call:', error);
            throw error;
        }
    },

    addNotification: (notification) => {
        set(state => ({
            notifications: [
                {
                    id: notification.id || Date.now().toString(),
                    timestamp: notification.timestamp || new Date(),
                    ...notification
                },
                ...state.notifications
            ]
        }));
    },

    removeNotification: (notificationId) => {
        set(state => ({
            notifications: state.notifications.filter(n => n.id !== notificationId)
        }));
    },

    clearNotifications: () => {
        set({ notifications: [] });
    },

    fetchCallHistory: async () => {
        try {
            await new Promise(resolve => setTimeout(resolve, 300));
            const { callHistory } = get();

            if (callHistory.length === 0) {
                const mockCallHistory = [
                    {
                        id: '1',
                        type: 'inbound',
                        phoneNumber: '5551234567',
                        startTime: new Date(Date.now() - 3600000),
                        endTime: new Date(Date.now() - 3550000),
                        duration: 600,
                        disposition: 'Information Provided'
                    },
                    {
                        id: '2',
                        type: 'outbound',
                        phoneNumber: '5559876543',
                        startTime: new Date(Date.now() - 7200000),
                        endTime: new Date(Date.now() - 7170000),
                        duration: 300,
                        disposition: 'Sale Completed'
                    },
                    {
                        id: '3',
                        type: 'inbound',
                        phoneNumber: '5552223333',
                        startTime: new Date(Date.now() - 10800000),
                        endTime: new Date(Date.now() - 10780000),
                        duration: 180,
                        disposition: 'Issue Resolved'
                    },
                    {
                        id: '4',
                        type: 'outbound',
                        phoneNumber: '5554445555',
                        startTime: new Date(Date.now() - 14400000),
                        endTime: new Date(Date.now() - 14385000),
                        duration: 240,
                        disposition: 'Callback Scheduled'
                    },
                    {
                        id: '5',
                        type: 'inbound',
                        phoneNumber: '5556667777',
                        startTime: new Date(Date.now() - 18000000),
                        endTime: new Date(Date.now() - 17970000),
                        duration: 480,
                        disposition: 'Escalated to Manager'
                    }
                ];

                set({ callHistory: mockCallHistory });
                return mockCallHistory;
            }

            return callHistory;
        } catch (error) {
            console.error('Failed to fetch call history:', error);
            throw error;
        }
    },

    initializeMockData: async () => {
        try {
            await new Promise(resolve => setTimeout(resolve, 300));

            const mockCallbacks = [
                {
                    id: '1',
                    phoneNumber: '5551234567',
                    scheduledTime: new Date(Date.now() + 3600000),
                    reason: 'Customer requested pricing information',
                    notes: 'Prepare quote for premium package'
                },
                {
                    id: '2',
                    phoneNumber: '5559876543',
                    scheduledTime: new Date(Date.now() + 86400000),
                    reason: 'Follow-up on service issue',
                    notes: 'Check if technician visit resolved the problem'
                },
                {
                    id: '3',
                    phoneNumber: '5552223333',
                    scheduledTime: new Date(Date.now() - 1800000),
                    reason: 'Callback requested',
                    notes: 'Customer was busy, asked to call back'
                }
            ];

            const mockMissedCalls = [
                {
                    id: '1',
                    phoneNumber: '5554445555',
                    time: new Date(Date.now() - 900000),
                    reason: 'Agent unavailable'
                },
                {
                    id: '2',
                    phoneNumber: '5556667777',
                    time: new Date(Date.now() - 3600000),
                    reason: 'Call abandoned'
                }
            ];

            set({
                callbacks: mockCallbacks,
                missedCalls: mockMissedCalls
            });

            return { success: true };
        } catch (error) {
            console.error('Failed to initialize mock data:', error);
            throw error;
        }
    },
}));

export default useAgentStore;
