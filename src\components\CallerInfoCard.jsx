import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Phone, User, Clock, FileText, ChevronDown, ChevronUp } from 'lucide-react';
import { ScrollArea } from './ui/scroll-area';

const CallerInfoCard = ({ callData }) => {
  const [expanded, setExpanded] = useState(true);

  if (!callData) return null;

  // Mock customer data - in a real app, this would be fetched from an API
  const customerData = callData.phoneNumber ? {
    name: '<PERSON>',
    email: '<EMAIL>',
    accountId: 'ACC-' + callData.phoneNumber.substring(callData.phoneNumber.length - 4),
    lastContact: '3 days ago',
    notes: 'Customer called about billing issue last week. Prefers email communication.',
    tags: ['Premium', 'Billing Issue']
  } : null;

  const formatTime = (date) => {
    if (!date) return '--';
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Card className="shadow-sm">
      <CardHeader className="py-3 px-4 flex flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium">Caller Information</CardTitle>
        <Button variant="ghost" size="sm" onClick={() => setExpanded(!expanded)} className="h-8 w-8 p-0">
          {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </CardHeader>

      {expanded && (
        <CardContent className="px-4 pb-3">
          <div className="space-y-3">
            {/* Call Details */}
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="rounded-md bg-gray-50 p-2">
                <p className="text-xs font-medium text-gray-500">Call Type</p>
                <div className="flex items-center">
                  <Phone className="h-3.5 w-3.5 mr-1 text-primary" />
                  <p className="font-medium capitalize">{callData.type}</p>
                </div>
              </div>

              <div className="rounded-md bg-gray-50 p-2">
                <p className="text-xs font-medium text-gray-500">Phone Number</p>
                <p className="font-medium">{callData.phoneNumber}</p>
              </div>

              <div className="rounded-md bg-gray-50 p-2">
                <p className="text-xs font-medium text-gray-500">Start Time</p>
                <div className="flex items-center">
                  <Clock className="h-3.5 w-3.5 mr-1 text-primary" />
                  <p className="font-medium">{formatTime(callData.startTime)}</p>
                </div>
              </div>

              <div className="rounded-md bg-gray-50 p-2">
                <p className="text-xs font-medium text-gray-500">Queue</p>
                <p className="font-medium">General</p>
              </div>
            </div>

            {/* Customer Information - Only show if we have customer data */}
            {customerData && (
              <>
                <div className="pt-1">
                  <h4 className="text-xs font-semibold uppercase text-gray-500 mb-2">Customer Details</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="rounded-md bg-gray-50 p-2">
                      <p className="text-xs font-medium text-gray-500">Name</p>
                      <div className="flex items-center">
                        <User className="h-3.5 w-3.5 mr-1 text-primary" />
                        <p className="font-medium">{customerData.name}</p>
                      </div>
                    </div>

                    <div className="rounded-md bg-gray-50 p-2">
                      <p className="text-xs font-medium text-gray-500">Account ID</p>
                      <p className="font-medium">{customerData.accountId}</p>
                    </div>

                    <div className="rounded-md bg-gray-50 p-2">
                      <p className="text-xs font-medium text-gray-500">Email</p>
                      <p className="font-medium text-xs truncate">{customerData.email}</p>
                    </div>

                    <div className="rounded-md bg-gray-50 p-2">
                      <p className="text-xs font-medium text-gray-500">Last Contact</p>
                      <p className="font-medium">{customerData.lastContact}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center mb-1">
                    <FileText className="h-3.5 w-3.5 mr-1 text-primary" />
                    <h4 className="text-xs font-semibold">Previous Notes</h4>
                  </div>
                  <ScrollArea className="h-16 rounded-md border p-2 bg-gray-50">
                    <p className="text-xs">{customerData.notes}</p>
                  </ScrollArea>
                </div>

                <div className="flex flex-wrap gap-1">
                  {customerData.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default CallerInfoCard;
