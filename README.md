# Live Call Center Dashboard

A modern, real-time dashboard for monitoring call center operations with comprehensive metrics, agent status tracking, and queue management.

## 🚀 Features

### 📊 Real-time Metrics

- **Overall Call Center Summary**
  - Total Active Agents (Available/Busy/On Break)
  - Total Calls in Progress
  - Total Calls Handled Today
  - Service Level (% of calls answered within 30 seconds)
  - Average Wait Time and Handling Time

### 📥 Inbound Call Monitoring

- Inbound Calls in Queue
- Longest Wait Time
- Abandoned Calls
- Inbound Calls Answered
- Service Level Agreement (SLA) tracking

### 📤 Outbound Call Tracking

- Outbound Calls Dialed
- Outbound Calls Connected
- Call Connect Rate (%)
- Outbound Calls per Agent
- Campaign Success Rate

### 👨‍💼 Agent Management

- **Real-time Agent Status Grid**
  - Agent Name/ID and Extension
  - Current Status (Available, On Call, Wrap-up, Break, Not Ready)
  - Call Duration for active calls
  - Daily Call Count (Inbound/Outbound)
  - Performance Metrics (AHT, FCR, Conversion Rate)

### 📋 Call Details

- **Active Calls Table**
  - Caller Number/Customer Name
  - Agent Handling the Call
  - Call Start Time and Duration
  - Call Type (Inbound/Outbound)
  - Call Status and Disposition

### 📈 Visual Analytics

- **Real-time Charts**
  - Call Volume Trends
  - Agent Status Distribution
  - Service Level Trends
  - Queue Performance Heatmaps

### 🎛️ Queue Management

- **Queue Selection Filtering**
  - Filter all metrics by specific queue
  - Queue-specific performance metrics
  - Service level monitoring per queue
  - Wait time analytics

## 🛠️ Technology Stack

- **Frontend**: React 19 with Vite
- **UI Components**: Radix UI + Tailwind CSS
- **State Management**: Zustand
- **Icons**: Lucide React
- **Styling**: Tailwind CSS with custom components

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd nucleus-dashboard
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start the development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` (or the port shown in terminal)

### Build for Production

```bash
npm run build
npm run preview
```

## 🎨 Dashboard Layout

### Header Section

- Dashboard title and description
- Queue selection dropdown
- Live status indicator

### Main Metrics Grid

- 12 key performance indicators displayed as cards
- Color-coded status indicators
- Real-time value updates

### Secondary Content

- **Left Column**: Queue Status Cards
- **Right Column**: Real-time Charts with tabs

### Agent Status Grid

- Responsive grid layout
- Agent cards with status indicators
- Performance metrics per agent

### Active Calls Table

- Sortable columns
- Real-time call status updates
- Agent and customer information

## 🔧 Configuration

### Queue Setup

Queues are configured in `src/lib/dashboardStore.js`:

```javascript
const generateMockQueues = () => [
  {
    id: "sales",
    name: "Sales",
    // ... other properties
  },
  // Add more queues as needed
];
```

### Refresh Interval

Default refresh interval is 5 seconds. Modify in `LiveDashboardPage.jsx`:

```javascript
const [refreshInterval, setRefreshInterval] = useState(5000); // 5 seconds
```

## 📱 Responsive Design

The dashboard is fully responsive and works on:

- Desktop (1920px+)
- Laptop (1024px+)
- Tablet (768px+)
- Mobile (320px+)

## 🎯 Key Metrics Explained

- **Service Level**: Percentage of calls answered within 30 seconds
- **Average Handle Time (AHT)**: Average duration of calls including talk time and wrap-up
- **First Call Resolution (FCR)**: Percentage of calls resolved on first contact
- **Abandon Rate**: Percentage of callers who hang up while waiting
- **Connect Rate**: Percentage of outbound calls that successfully connect

## 🔄 Real-time Updates

The dashboard automatically refreshes data every 5 seconds to provide real-time insights. The refresh rate can be adjusted based on your needs.

## 🎨 Customization

### Colors and Themes

Modify colors in `src/styles/globals.css` and component files.

### Adding New Metrics

1. Update the data structure in `dashboardStore.js`
2. Add new metric cards in `CallCenterMetrics.jsx`
3. Update the mock data generators

### Custom Components

All UI components are in `src/components/ui/` and can be customized as needed.

## 📊 Data Integration

Currently uses mock data for demonstration. To integrate with real data:

1. Replace mock data generators in `dashboardStore.js`
2. Implement API calls to your call center system
3. Update the data fetching logic in `fetchDashboardData`

## 🚀 Deployment

The dashboard can be deployed to any static hosting service:

- Vercel
- Netlify
- AWS S3 + CloudFront
- GitHub Pages

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Support

For support and questions, please open an issue in the repository.
