import { useEffect } from 'react';
import Sidebar from '../components/Sidebar';
import StatusBar from '../components/StatusBar';
import AgentDashboard from '../components/AgentDashboard';
import CallHistoryTable from '../components/CallHistoryTable';
import useAgentStore from '../lib/agentStore';

const AgentPerformancePage = () => {
    const {
        fetchCallHistory,
        initializeMockData
    } = useAgentStore();

    useEffect(() => {
        const loadInitialData = async () => {
            try {
                await Promise.all([
                    fetchCallHistory(),
                    initializeMockData()
                ]);
            } catch (error) {
                console.error('Failed to load initial data:', error);
            }
        };

        loadInitialData();
    }, [fetchCallHistory, initializeMockData]);

    return (
        <div className="h-screen bg-gray-50 flex flex-col md:flex-row overflow-hidden">
            <Sidebar />

            <main className="flex-1 flex flex-col h-screen overflow-hidden">
                <StatusBar />

                <div className="p-4 flex-1 overflow-auto">
                    <div className="space-y-4">
                        <h1 className="text-2xl font-bold">Agent Performance</h1>

                        <AgentDashboard />

                        <div className="mt-6">
                            <h2 className="text-xl font-semibold mb-4">Recent Call History</h2>
                            <CallHistoryTable />
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
};

export default AgentPerformancePage;
