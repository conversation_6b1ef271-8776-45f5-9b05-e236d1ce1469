import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { 
  Phone, 
  Clock, 
  AlertCircle, 
  TrendingUp,
  PhoneMissed,
  Users
} from 'lucide-react';

const formatTime = (seconds) => {
  if (seconds < 60) return `${seconds}s`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
};

const getServiceLevelColor = (level) => {
  if (level >= 90) return 'text-green-600';
  if (level >= 80) return 'text-yellow-600';
  return 'text-red-600';
};

const QueueCard = ({ queue }) => {
  const serviceLevelColor = getServiceLevelColor(queue.serviceLevel);
  
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">{queue.name}</CardTitle>
          <Badge 
            variant={queue.waitingCalls > 5 ? "destructive" : queue.waitingCalls > 0 ? "secondary" : "default"}
            className="font-medium"
          >
            {queue.waitingCalls} waiting
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Service Level */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600">Service Level</span>
            <span className={`text-sm font-bold ${serviceLevelColor}`}>
              {queue.serviceLevel}%
            </span>
          </div>
          <Progress 
            value={queue.serviceLevel} 
            className="h-2"
          />
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-xs text-gray-600">Longest Wait</span>
            </div>
            <p className="text-sm font-semibold text-gray-900">
              {formatTime(queue.longestWait)}
            </p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center space-x-1">
              <TrendingUp className="h-4 w-4 text-gray-500" />
              <span className="text-xs text-gray-600">Avg Wait</span>
            </div>
            <p className="text-sm font-semibold text-gray-900">
              {formatTime(queue.avgWaitTime)}
            </p>
          </div>
        </div>

        {/* Abandoned Calls */}
        <div className="flex items-center justify-between p-2 bg-red-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <PhoneMissed className="h-4 w-4 text-red-600" />
            <span className="text-sm font-medium text-red-700">Abandoned</span>
          </div>
          <span className="text-sm font-bold text-red-700">{queue.abandonedCalls}</span>
        </div>

        {/* Status Indicator */}
        <div className="flex items-center justify-center pt-2">
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium ${
            queue.waitingCalls === 0 
              ? 'bg-green-100 text-green-700' 
              : queue.waitingCalls <= 3 
                ? 'bg-yellow-100 text-yellow-700'
                : 'bg-red-100 text-red-700'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              queue.waitingCalls === 0 
                ? 'bg-green-500' 
                : queue.waitingCalls <= 3 
                  ? 'bg-yellow-500'
                  : 'bg-red-500 animate-pulse'
            }`}></div>
            <span>
              {queue.waitingCalls === 0 
                ? 'All Clear' 
                : queue.waitingCalls <= 3 
                  ? 'Normal Load'
                  : 'High Volume'
              }
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const CallQueueStatus = ({ queues, isLoading }) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Phone className="h-5 w-5" />
            <span>Queue Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="h-5 bg-gray-200 rounded w-20"></div>
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="h-4 bg-gray-200 rounded w-24"></div>
                      <div className="h-4 bg-gray-200 rounded w-12"></div>
                    </div>
                    <div className="h-2 bg-gray-200 rounded"></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <div className="h-3 bg-gray-200 rounded w-20"></div>
                      <div className="h-4 bg-gray-200 rounded w-16"></div>
                    </div>
                    <div className="space-y-1">
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                      <div className="h-4 bg-gray-200 rounded w-12"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalWaiting = queues.reduce((sum, queue) => sum + queue.waitingCalls, 0);
  const avgServiceLevel = queues.length > 0 
    ? Math.round(queues.reduce((sum, queue) => sum + queue.serviceLevel, 0) / queues.length)
    : 0;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Phone className="h-5 w-5" />
            <span>Queue Status</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {totalWaiting} total waiting
            </Badge>
            <Badge 
              variant="secondary" 
              className={`text-xs ${getServiceLevelColor(avgServiceLevel)}`}
            >
              {avgServiceLevel}% SLA
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {queues.map((queue) => (
            <QueueCard key={queue.id} queue={queue} />
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default CallQueueStatus;
