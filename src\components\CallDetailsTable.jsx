import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback } from './ui/avatar';
import {
    Phone,
    PhoneIncoming,
    PhoneOutgoing,
    Clock,
    User,
    MapPin
} from 'lucide-react';

const getCallTypeIcon = (type) => {
    return type === 'Inbound'
        ? <PhoneIncoming className="h-4 w-4 text-green-600" />
        : <PhoneOutgoing className="h-4 w-4 text-blue-600" />;
};

const getStatusBadge = (status) => {
    const statusConfig = {
        'Connected': { variant: 'default', color: 'bg-green-100 text-green-800' },
        'Ringing': { variant: 'secondary', color: 'bg-yellow-100 text-yellow-800' },
        'Hold': { variant: 'secondary', color: 'bg-orange-100 text-orange-800' },
        'Transferring': { variant: 'secondary', color: 'bg-blue-100 text-blue-800' }
    };

    const config = statusConfig[status] || statusConfig['Connected'];

    return (
        <Badge className={`${config.color} border-0 text-xs`}>
            {status}
        </Badge>
    );
};

const formatDuration = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const formatTime = (date) => {
    return new Date(date).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });
};

const CallRow = ({ call, agent }) => {
    const agentInitials = agent ? agent.name.split(' ').map(n => n[0]).join('').toUpperCase() : '??';

    return (
        <tr className="hover:bg-gray-50 transition-colors">
            <td className="px-4 py-3">
                <div className="flex items-center space-x-3">
                    {getCallTypeIcon(call.type)}
                    <div>
                        <p className="font-medium text-gray-900">{call.phoneNumber}</p>
                        <p className="text-sm text-gray-500">{call.customerName}</p>
                    </div>
                </div>
            </td>

            <td className="px-4 py-3">
                <div className="flex items-center space-x-2">
                    <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                            {agentInitials}
                        </AvatarFallback>
                    </Avatar>
                    <span className="text-sm font-medium text-gray-900">
                        {agent ? agent.name : 'Unknown'}
                    </span>
                </div>
            </td>

            <td className="px-4 py-3">
                <Badge variant="outline" className="text-xs">
                    {call.queue}
                </Badge>
            </td>

            <td className="px-4 py-3">
                <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-sm text-gray-600">{formatTime(call.startTime)}</span>
                </div>
            </td>

            <td className="px-4 py-3">
                <span className="text-sm font-medium text-gray-900">
                    {formatDuration(call.duration)}
                </span>
            </td>

            <td className="px-4 py-3">
                {getStatusBadge(call.status)}
            </td>
        </tr>
    );
};

const CallDetailsTable = ({ calls, agents }) => {

    const getAgentById = (agentId) => {
        return agents.find(agent => agent.id === agentId);
    };

    const activeCalls = calls.filter(call =>
        ['Connected', 'Ringing', 'Hold', 'Transferring'].includes(call.status)
    );

    const callsByType = activeCalls.reduce((acc, call) => {
        acc[call.type] = (acc[call.type] || 0) + 1;
        return acc;
    }, {});

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                        <Phone className="h-5 w-5" />
                        <span>Active Calls</span>
                        <Badge variant="outline">{activeCalls.length} active</Badge>
                    </CardTitle>

                    <div className="flex items-center space-x-2">
                        {Object.entries(callsByType).map(([type, count]) => (
                            <Badge
                                key={type}
                                variant="secondary"
                                className={`text-xs ${type === 'Inbound'
                                        ? 'bg-green-100 text-green-700'
                                        : 'bg-blue-100 text-blue-700'
                                    }`}
                            >
                                {type}: {count}
                            </Badge>
                        ))}
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                {activeCalls.length === 0 ? (
                    <div className="text-center py-8">
                        <Phone className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">No active calls at the moment</p>
                    </div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead>
                                <tr className="border-b border-gray-200">
                                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Caller
                                    </th>
                                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Agent
                                    </th>
                                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Queue
                                    </th>
                                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Start Time
                                    </th>
                                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Duration
                                    </th>
                                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200">
                                {activeCalls.map((call) => (
                                    <CallRow
                                        key={call.id}
                                        call={call}
                                        agent={getAgentById(call.agentId)}
                                    />
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default CallDetailsTable;
