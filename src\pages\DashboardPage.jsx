import { useEffect, useState } from 'react';
import Sidebar from '../components/Sidebar';
import StatusBar from '../components/StatusBar';
import Dialer from '../components/Dialer';
import WorkCodeDrawer from '../components/WorkCodeDrawer';
import ScriptWidget from '../components/ScriptWidget';
import CallerInfoCard from '../components/CallerInfoCard';
import CallbackList from '../components/CallbackList';
import MissedCallsWidget from '../components/MissedCallsWidget';
import useAgentStore from '../lib/agentStore';

const DashboardPage = () => {
    const [afterCallData, setAfterCallData] = useState(null);

    const {
        fetchQueues,
        fetchWorkCodes,
        fetchBreakCodes,
        callData,
        setReady,
        initializeMockData,
        checkQueueLogin,
        checkAgentReady
    } = useAgentStore();

    useEffect(() => {
        const loadInitialData = async () => {
            try {
                // First, check queue login and agent ready status
                await checkQueueLogin();
                await checkAgentReady();

                // Then load all the data
                await Promise.all([
                    fetchQueues(),
                    fetchWorkCodes(),
                    fetchBreakCodes(),
                    initializeMockData()
                ]);
            } catch (error) {
                console.error('Failed to load initial data:', error);
            }
        };

        loadInitialData();
    }, [fetchQueues, fetchWorkCodes, fetchBreakCodes, initializeMockData, checkQueueLogin, checkAgentReady]);

    return (
        <div className="h-screen bg-gray-50 flex flex-col md:flex-row overflow-hidden">
            <Sidebar />

            <main className="flex-1 flex flex-col h-screen overflow-hidden">
                <StatusBar />

                <div className="p-4 flex-1 overflow-auto">
                    <div className="flex flex-col space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="w-full">
                                <Dialer onCallEnded={setAfterCallData} />
                            </div>
                            <div className="w-full">
                                <ScriptWidget />
                            </div>
                        </div>

                        {callData && <CallerInfoCard callData={callData} />}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="w-full">
                                <CallbackList />
                            </div>
                            <div className="w-full">
                                <MissedCallsWidget />
                            </div>
                        </div>

                        <WorkCodeDrawer
                            callData={afterCallData}
                            onClose={() => setAfterCallData(null)}
                            onSubmit={() => {
                                setReady();
                                setAfterCallData(null);
                            }}
                        />
                    </div>
                </div>
            </main>
        </div>
    );
};

export default DashboardPage;
