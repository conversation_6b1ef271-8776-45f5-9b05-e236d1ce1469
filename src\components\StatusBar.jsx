import { useState, useEffect, useRef } from 'react';
import { Button } from './ui/button';
import useAgentStore from '../lib/agentStore';
import sipService from '../lib/sipService';
import { Clock, PauseCircle, PlayCircle, Users } from 'lucide-react';

const StatusBar = () => {
    const { status, statusReason, setReady, setNotReady, breakCodes, setBreak, queueLoggedIn, queueLogin, queueLogout, queues } = useAgentStore();
    const [showBreakMenu, setShowBreakMenu] = useState(false);
    const [statusDuration, setStatusDuration] = useState(0);
    const timerRef = useRef(null);
    const breakMenuRef = useRef(null);

    useEffect(() => {
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }

        setStatusDuration(0);

        if (status === 'READY' || status === 'BREAK') {
            timerRef.current = setInterval(() => {
                setStatusDuration(prev => prev + 1);
            }, 1000);
        }

        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, [status]);
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (breakMenuRef.current && !breakMenuRef.current.contains(event.target)) {
                setShowBreakMenu(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const formatDuration = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const getStatusColor = () => {
        switch (status) {
            case 'READY':
                return 'bg-green-500';
            case 'NOT_READY':
                return 'bg-yellow-500';
            case 'ON_CALL':
                return 'bg-blue-500';
            case 'BREAK':
                return 'bg-purple-500';
            default:
                return 'bg-gray-500';
        }
    };

    const getStatusTextColor = () => {
        switch (status) {
            case 'READY':
                return 'text-green-600';
            case 'NOT_READY':
                return 'text-yellow-600';
            case 'ON_CALL':
                return 'text-blue-600';
            case 'BREAK':
                return 'text-purple-600';
            default:
                return 'text-gray-600';
        }
    };

    const getStatusIcon = () => {
        switch (status) {
            case 'READY':
                return <PlayCircle className="w-5 h-5 text-green-500" />;
            case 'BREAK':
                return <PauseCircle className="w-5 h-5 text-purple-500" />;
            default:
                return null;
        }
    };

    const handleReadyClick = async () => {
        try {
            await sipService.register();
            await setReady();
        } catch (error) {
            console.error('Failed to set status to READY:', error);
        }
    };

    const handleNotReadyClick = async () => {
        try {
            await sipService.unregister();
            await setNotReady('Agent initiated');
        } catch (error) {
            console.error('Failed to set status to NOT_READY:', error);
        }
    };

    const handleBreakClick = () => {
        setShowBreakMenu(!showBreakMenu);
    };

    const handleBreakSelect = async (breakCode) => {
        try {
            await sipService.unregister();
            await setBreak(breakCode);
            setShowBreakMenu(false);
        } catch (error) {
            console.error('Failed to set status to BREAK:', error);
        }
    };

    const handleQueueToggle = async () => {
        try {
            if (queueLoggedIn) {
                await queueLogout();
            } else {
                const allQueueIds = queues.map(queue => queue.id);
                await queueLogin(allQueueIds);
            }
        } catch (error) {
            console.error('Failed to toggle queue login:', error);
        }
    };

    return (
        <div className="flex-shrink-0 bg-white border-b shadow-sm">
            <div className="flex items-center h-16 px-4 mx-auto">
                <div className="grid items-center w-full grid-cols-1 gap-3 md:grid-cols-3">
                    {/* Left section - Logo */}
                    <div className="items-center hidden md:flex">
                        <h1 className="text-xl font-bold text-gray-900">Agent Portal</h1>
                    </div>

                    {/* Middle section - Status and Timer */}
                    <div className="flex items-center justify-center">
                        {(status === 'READY' || status === 'BREAK') ? (
                            <div className="flex items-center gap-2">
                                <div className="flex items-center">
                                    <div className={`mr-2 h-3 w-3 rounded-full ${getStatusColor()}`}></div>
                                    <span className={`font-medium ${getStatusTextColor()} truncate max-w-[80px] sm:max-w-none`}>
                                        {status}
                                        {statusReason && ` (${statusReason})`}
                                    </span>
                                </div>
                                <div className="flex items-center text-sm text-gray-600 bg-gray-100 px-3 py-1.5 rounded-full">
                                    <Clock className="mr-1.5 h-4 w-4" />
                                    <span className="font-medium">{formatDuration(statusDuration)}</span>
                                </div>
                            </div>
                        ) : (
                            <div className="flex items-center">
                                <div className={`mr-2 h-3 w-3 rounded-full ${getStatusColor()}`}></div>
                                <span className={`font-medium ${getStatusTextColor()} truncate max-w-[80px] sm:max-w-none`}>
                                    {status}
                                    {statusReason && ` (${statusReason})`}
                                </span>
                            </div>
                        )}
                    </div>

                    {/* Right section - Status Controls */}
                    <div className="flex justify-center space-x-2 md:justify-end">
                        <Button
                            onClick={handleQueueToggle}
                            variant={queueLoggedIn ? 'default' : 'outline'}
                            className={`h-10 px-3 ${queueLoggedIn ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                            disabled={status === 'ON_CALL'}
                            size="sm"
                        >
                            <Users className="w-4 h-4 mr-1" />
                            {queueLoggedIn ? 'Queues: On' : 'Queues: Off'}
                        </Button>

                        <Button
                            onClick={handleReadyClick}
                            variant={status === 'READY' ? 'default' : 'outline'}
                            className={`h-10 px-3 ${status === 'READY' ? 'bg-green-600 hover:bg-green-700' : ''}`}
                            disabled={status === 'ON_CALL'}
                            size="sm"
                        >
                            Ready
                        </Button>

                        <Button
                            onClick={handleNotReadyClick}
                            variant={status === 'NOT_READY' ? 'default' : 'outline'}
                            className={`h-10 px-3 ${status === 'NOT_READY' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}`}
                            disabled={status === 'ON_CALL'}
                            size="sm"
                        >
                            Not Ready
                        </Button>

                        <div className="relative" ref={breakMenuRef}>
                            <Button
                                onClick={handleBreakClick}
                                variant={status === 'BREAK' ? 'default' : 'outline'}
                                className={`h-10 px-3 ${status === 'BREAK' ? 'bg-purple-600 hover:bg-purple-700' : ''}`}
                                disabled={status === 'ON_CALL'}
                                size="sm"
                            >
                                Break
                            </Button>

                            {showBreakMenu && (
                                <div className="absolute right-0 z-10 w-64 py-1 mt-1 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
                                    {breakCodes.map((breakCode) => (
                                        <button
                                            key={breakCode.id}
                                            className="flex items-center w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100"
                                            onClick={() => handleBreakSelect(breakCode.id)}
                                        >
                                            <span>{breakCode.name}</span>
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StatusBar;
