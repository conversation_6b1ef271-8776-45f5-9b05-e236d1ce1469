import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import useAgentStore from '../lib/agentStore';

const WorkCodeSubmission = () => {
    const { callData, workCodes, fetchWorkCodes, submitWorkCode } = useAgentStore();
    const [selectedWorkCode, setSelectedWorkCode] = useState('');
    const [notes, setNotes] = useState('');
    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);

    useEffect(() => {
        const loadWorkCodes = async () => {
            try {
                await fetchWorkCodes();
            } catch (error) {
                console.error('Failed to fetch work codes:', error);
            }
        };

        loadWorkCodes();
    }, [fetchWorkCodes]);

    useEffect(() => {
        setSelectedWorkCode('');
        setNotes('');
        setSuccess(false);
    }, [callData]);

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!callData || !selectedWorkCode) return;

        setLoading(true);
        try {
            await submitWorkCode(callData.id, selectedWorkCode, notes);
            setSuccess(true);
            setSelectedWorkCode('');
            setNotes('');
        } catch (error) {
            console.error('Failed to submit work code:', error);
        } finally {
            setLoading(false);
        }
    };

    if (!callData) {
        return null;
    }

    return (
        <div className="p-4 border rounded-lg shadow-sm">
            <h3 className="mb-4 text-lg font-medium">Call Disposition</h3>

            {success ? (
                <div className="p-4 text-sm text-green-700 rounded-md bg-green-50">
                    Work code submitted successfully.
                </div>
            ) : (
                <form onSubmit={handleSubmit}>
                    <div className="mb-4">
                        <label className="block mb-1 text-sm font-medium">
                            Select Work Code
                        </label>
                        <select
                            value={selectedWorkCode}
                            onChange={(e) => setSelectedWorkCode(e.target.value)}
                            className="w-full p-2 text-sm border border-gray-300 rounded-md focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                            required
                        >
                            <option value="">Select a work code</option>
                            {workCodes.map((code) => (
                                <option key={code.id} value={code.id}>
                                    {code.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="mb-4">
                        <label className="block mb-1 text-sm font-medium">
                            Notes (Optional)
                        </label>
                        <textarea
                            value={notes}
                            onChange={(e) => setNotes(e.target.value)}
                            className="w-full p-2 text-sm border border-gray-300 rounded-md focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                            rows={3}
                        />
                    </div>

                    <Button
                        type="submit"
                        disabled={!selectedWorkCode || loading}
                        className="w-full"
                    >
                        {loading ? 'Submitting...' : 'Submit Work Code'}
                    </Button>
                </form>
            )}
        </div>
    );
};

export default WorkCodeSubmission;
