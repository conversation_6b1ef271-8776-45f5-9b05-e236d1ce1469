import { useState, useEffect, useRef } from 'react';
import { Button } from './ui/button';
import useAgentStore from '../lib/agentStore';
import sipService from '../lib/sipService';
import { Clock, PauseCircle, PlayCircle } from 'lucide-react';

const AgentStatus = () => {
    const { status, statusReason, setReady, setNotReady, breakCodes, setBreak } = useAgentStore();
    const [showBreakMenu, setShowBreakMenu] = useState(false);
    const [statusDuration, setStatusDuration] = useState(0);
    const timerRef = useRef(null);

    // Status timer
    useEffect(() => {
        // Clear previous timer
        if (timerRef.current) {
            clearInterval(timerRef.current);
            timerRef.current = null;
        }

        // Reset duration when status changes
        setStatusDuration(0);

        // Start timer for READY and BREAK states
        if (status === 'READY' || status === 'BREAK') {
            timerRef.current = setInterval(() => {
                setStatusDuration(prev => prev + 1);
            }, 1000);
        }

        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, [status]);

    // Format duration as HH:MM:SS
    const formatDuration = (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const getStatusColor = () => {
        switch (status) {
            case 'READY':
                return 'bg-green-500';
            case 'NOT_READY':
                return 'bg-yellow-500';
            case 'ON_CALL':
                return 'bg-blue-500';
            case 'BREAK':
                return 'bg-purple-500';
            default:
                return 'bg-gray-500';
        }
    };

    const getStatusIcon = () => {
        switch (status) {
            case 'READY':
                return <PlayCircle className="h-5 w-5 text-green-500" />;
            case 'BREAK':
                return <PauseCircle className="h-5 w-5 text-purple-500" />;
            default:
                return null;
        }
    };

    const handleReadyClick = async () => {
        try {
            // Register with SIP server when going to READY state
            await sipService.register();
            await setReady();
        } catch (error) {
            console.error('Failed to set status to READY:', error);
        }
    };

    const handleNotReadyClick = async () => {
        try {
            // Unregister from SIP server when going to NOT_READY state
            await sipService.unregister();
            await setNotReady('Agent initiated');
        } catch (error) {
            console.error('Failed to set status to NOT_READY:', error);
        }
    };

    const handleBreakClick = () => {
        setShowBreakMenu(!showBreakMenu);
    };

    const handleBreakSelect = async (breakCode) => {
        try {
            // Unregister from SIP server when going on break
            await sipService.unregister();
            await setBreak(breakCode);
            setShowBreakMenu(false);
        } catch (error) {
            console.error('Failed to set break:', error);
        }
    };

    return (
        <div className="rounded-lg border p-4 shadow-sm">
            <div className="mb-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center">
                        <div className={`mr-2 h-3 w-3 rounded-full ${getStatusColor()}`}></div>
                        <h3 className="text-lg font-medium">
                            Status: {status}
                            {statusReason && ` (${statusReason})`}
                        </h3>
                    </div>

                    {(status === 'READY' || status === 'BREAK') && (
                        <div className="flex items-center text-sm text-gray-600">
                            <Clock className="mr-1 h-4 w-4" />
                            <span>{formatDuration(statusDuration)}</span>
                        </div>
                    )}
                </div>

                {/* Status timer display */}
                {(status === 'READY' || status === 'BREAK') && (
                    <div className="mt-2 flex items-center rounded-md bg-gray-100 p-2">
                        {getStatusIcon()}
                        <div className="ml-2">
                            <p className="text-sm font-medium">
                                {status === 'READY' ? 'Ready Time' : 'Break Time'}
                            </p>
                            <p className="text-xs text-gray-500">
                                {formatDuration(statusDuration)}
                            </p>
                        </div>
                    </div>
                )}
            </div>

            <div className="flex space-x-2">
                <Button
                    onClick={handleReadyClick}
                    variant={status === 'READY' ? 'default' : 'outline'}
                    className={status === 'READY' ? 'bg-green-600 hover:bg-green-700' : ''}
                    disabled={status === 'ON_CALL'}
                >
                    Ready
                </Button>

                <Button
                    onClick={handleNotReadyClick}
                    variant={status === 'NOT_READY' ? 'default' : 'outline'}
                    className={status === 'NOT_READY' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
                    disabled={status === 'ON_CALL'}
                >
                    Not Ready
                </Button>

                <div className="relative">
                    <Button
                        onClick={handleBreakClick}
                        variant={status === 'BREAK' ? 'default' : 'outline'}
                        className={status === 'BREAK' ? 'bg-purple-600 hover:bg-purple-700' : ''}
                        disabled={status === 'ON_CALL'}
                    >
                        Break
                    </Button>

                    {showBreakMenu && (
                        <div className="absolute right-0 z-10 mt-2 w-64 rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5">
                            {breakCodes.map((breakCode) => (
                                <button
                                    key={breakCode.id}
                                    className="flex w-full items-center justify-between px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
                                    onClick={() => handleBreakSelect(breakCode.id)}
                                >
                                    <span>{breakCode.name}</span>
                                    <span className="ml-2 rounded-full bg-gray-200 px-2 py-1 text-xs text-gray-700">
                                        {breakCode.duration} min
                                    </span>
                                </button>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default AgentStatus;
