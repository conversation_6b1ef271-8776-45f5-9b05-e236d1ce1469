import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { PhoneMissed, Clock, ChevronDown, ChevronUp } from 'lucide-react';
import useAgentStore from '../lib/agentStore';
import sipService from '../lib/sipService';

const MissedCallsWidget = () => {
  const { missedCalls, removeMissedCall } = useAgentStore();
  const [expanded, setExpanded] = useState(true);
  const [loading, setLoading] = useState(false);
  const [activeCallId, setActiveCallId] = useState(null);

  // Sort missed calls by time (newest first)
  const sortedMissedCalls = [...missedCalls].sort((a, b) => 
    new Date(b.time) - new Date(a.time)
  );

  const formatTime = (date) => {
    if (!date) return '--';
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (date) => {
    if (!date) return '--';
    const today = new Date();
    const callDate = new Date(date);
    
    if (
      callDate.getDate() === today.getDate() &&
      callDate.getMonth() === today.getMonth() &&
      callDate.getFullYear() === today.getFullYear()
    ) {
      return 'Today';
    }
    
    return callDate.toLocaleDateString();
  };

  const handleCall = async (call) => {
    if (loading) return;
    
    setLoading(true);
    setActiveCallId(call.id);
    
    try {
      // Make the call
      const success = await sipService.makeCall(call.phoneNumber);
      
      if (success) {
        // Remove from missed calls list
        await removeMissedCall(call.id);
      }
    } catch (error) {
      console.error('Failed to call back missed call:', error);
    } finally {
      setLoading(false);
      setActiveCallId(null);
    }
  };

  if (missedCalls.length === 0) {
    return (
      <Card className="shadow-sm">
        <CardHeader className="py-3 px-4">
          <CardTitle className="text-sm font-medium">Missed Calls</CardTitle>
        </CardHeader>
        <CardContent className="px-4 pb-3 text-center text-sm text-gray-500">
          No missed calls
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="py-3 px-4 flex flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium">
          Missed Calls
          <Badge variant="destructive" className="ml-2">
            {missedCalls.length}
          </Badge>
        </CardTitle>
        <Button variant="ghost" size="sm" onClick={() => setExpanded(!expanded)} className="h-8 w-8 p-0">
          {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </CardHeader>

      {expanded && (
        <CardContent className="px-4 pb-3">
          <div className="space-y-2">
            {sortedMissedCalls.map((call) => (
              <div 
                key={call.id} 
                className="rounded-md border border-red-200 bg-red-50 p-3"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <PhoneMissed className="h-4 w-4 mr-1.5 text-red-500" />
                    <span className="font-medium">{call.phoneNumber}</span>
                  </div>
                  
                  <Button 
                    size="sm" 
                    className="h-8 text-xs"
                    onClick={() => handleCall(call)}
                    disabled={loading && activeCallId === call.id}
                  >
                    {loading && activeCallId === call.id ? 'Calling...' : 'Call Back'}
                  </Button>
                </div>
                
                <div className="flex items-center text-xs text-gray-600">
                  <Clock className="h-3.5 w-3.5 mr-1 text-gray-500" />
                  <span>{formatDate(call.time)} at {formatTime(call.time)}</span>
                </div>
                
                {call.reason && (
                  <div className="text-xs text-gray-600 mt-1">
                    <span className="font-medium">Reason:</span> {call.reason}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default MissedCallsWidget;
