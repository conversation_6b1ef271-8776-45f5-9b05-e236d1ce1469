import { useState, useEffect } from 'react';
import { But<PERSON> } from './ui/button';
import { X, Check, ClipboardList, Phone, Calendar, Clock, ArrowRight, User } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Calendar as CalendarComponent } from './ui/calendar';
import { format } from 'date-fns';
import useAgentStore from '../lib/agentStore';

const WorkCodeDrawer = ({ callData, onClose, onSubmit }) => {
    const { workCodes, submitWorkCode, scheduleCallback } = useAgentStore();
    const [selectedWorkCode, setSelectedWorkCode] = useState('');
    const [notes, setNotes] = useState('');
    const [loading, setLoading] = useState(false);
    const [isVisible, setIsVisible] = useState(false);
    const [closedCallData, setClosedCallData] = useState(null);
    const [showReopenButton, setShowReopenButton] = useState(false);
    const [activeTab, setActiveTab] = useState('disposition');
    const [callbackDate, setCallbackDate] = useState(new Date());
    const [callbackTime, setCallbackTime] = useState('12:00');
    const [callbackReason, setCallbackReason] = useState('');
    const [nextAction, setNextAction] = useState('');

    useEffect(() => {
        if (callData) {
            setIsVisible(true);
            setSelectedWorkCode('');
            setNotes('');
            setClosedCallData(callData);
            setShowReopenButton(false);
        } else {
            setIsVisible(false);
        }
    }, [callData]);

    const handleClose = () => {
        setIsVisible(false);
        setShowReopenButton(true);
        setTimeout(() => {
            onClose();
        }, 300);
    };

    const handleReopen = () => {
        if (closedCallData) {
            setIsVisible(true);
            setShowReopenButton(false);
        }
    };

    const handleSubmit = async () => {
        if (!closedCallData || !selectedWorkCode) return;

        setLoading(true);
        try {
            const workCode = workCodes.find(code => code.id === selectedWorkCode);
            const isCallback = workCode?.name === 'Callback Scheduled';

            await submitWorkCode(closedCallData.id, selectedWorkCode, notes);

            if (isCallback && callbackDate && callbackTime) {
                const [hours, minutes] = callbackTime.split(':').map(Number);
                const scheduledTime = new Date(callbackDate);
                scheduledTime.setHours(hours, minutes, 0, 0);

                await scheduleCallback({
                    id: Date.now().toString(),
                    phoneNumber: closedCallData.phoneNumber,
                    scheduledTime,
                    reason: callbackReason || 'Follow-up call',
                    notes: notes
                });
            }

            setLoading(false);
            onSubmit();
            handleClose();
            setClosedCallData(null);
            setShowReopenButton(false);
        } catch (error) {
            console.error('Failed to submit work code:', error);
            setLoading(false);
        }
    };

    if (!callData && !closedCallData) return null;

    return (
        <>
            {showReopenButton && !isVisible && (
                <div className="fixed z-40 bottom-4 right-4">
                    <Button
                        onClick={handleReopen}
                        className="flex items-center gap-2 shadow-lg"
                    >
                        <ClipboardList className="w-4 h-4" />
                        <span>Submit Work Code</span>
                    </Button>
                </div>
            )}

            <div className={`fixed inset-0 z-40 ${isVisible ? 'visible' : 'invisible'}`}>
                {/* Backdrop */}
                <div
                    className={`absolute inset-0 bg-black transition-opacity duration-300 ${isVisible ? 'opacity-50' : 'opacity-0'
                        }`}
                    onClick={handleClose}
                />

                {/* Drawer */}
                <div
                    className={`absolute bottom-0 left-0 right-0 transform bg-white p-4 shadow-lg transition-transform duration-300 ${isVisible ? 'translate-y-0' : 'translate-y-full'
                        }`}
                >
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium">After Call Work</h3>
                        <Button variant="ghost" size="sm" onClick={handleClose} className="w-8 h-8 p-0">
                            <X className="w-5 h-5" />
                        </Button>
                    </div>

                    {/* Call Summary */}
                    {closedCallData && (
                        <div className="p-3 mb-4 rounded-md bg-gray-50">
                            <h4 className="mb-2 text-sm font-medium">Call Summary</h4>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                                <div className="flex items-center">
                                    <Phone className="h-4 w-4 mr-1.5 text-gray-500" />
                                    <span className="capitalize">{closedCallData.type || 'Call'}</span>
                                </div>
                                <div className="flex items-center">
                                    <User className="h-4 w-4 mr-1.5 text-gray-500" />
                                    <span>{closedCallData.phoneNumber || 'Unknown'}</span>
                                </div>
                                {closedCallData.startTime && (
                                    <div className="flex items-center">
                                        <Clock className="h-4 w-4 mr-1.5 text-gray-500" />
                                        <span>
                                            {new Date(closedCallData.startTime).toLocaleTimeString([], {
                                                hour: '2-digit',
                                                minute: '2-digit'
                                            })}
                                        </span>
                                    </div>
                                )}
                                {closedCallData.duration && (
                                    <div>
                                        <span className="text-gray-500">Duration:</span>{' '}
                                        <span>
                                            {Math.floor(closedCallData.duration / 60)}:
                                            {(closedCallData.duration % 60).toString().padStart(2, '0')}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-4">
                        <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="disposition">Disposition</TabsTrigger>
                            <TabsTrigger value="callback">Callback</TabsTrigger>
                            <TabsTrigger value="next-action">Next Action</TabsTrigger>
                        </TabsList>

                        {/* Disposition Tab */}
                        <TabsContent value="disposition" className="mt-4">
                            <div className="grid grid-cols-1 gap-2 mb-4 sm:grid-cols-2 md:grid-cols-3">
                                {workCodes.map((code) => (
                                    <button
                                        key={code.id}
                                        onClick={() => setSelectedWorkCode(code.id)}
                                        className={`flex items-center justify-between rounded-md border p-3 text-left text-sm transition-colors ${selectedWorkCode === code.id
                                            ? 'border-primary bg-primary/10 text-primary'
                                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                            }`}
                                    >
                                        <span>{code.name}</span>
                                        {selectedWorkCode === code.id && <Check className="w-4 h-4" />}
                                    </button>
                                ))}
                            </div>

                            <div className="mb-4">
                                <label className="block mb-1 text-sm font-medium">Notes (Optional)</label>
                                <textarea
                                    value={notes}
                                    onChange={(e) => setNotes(e.target.value)}
                                    className="w-full p-2 text-sm border border-gray-300 rounded-md focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                                    rows={3}
                                    placeholder="Add any additional notes about the call..."
                                />
                            </div>
                        </TabsContent>

                        {/* Callback Tab */}
                        <TabsContent value="callback" className="mt-4">
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="callback-date" className="block mb-1 text-sm font-medium">Date</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className="justify-start w-full font-normal text-left"
                                                >
                                                    <Calendar className="w-4 h-4 mr-2" />
                                                    {callbackDate ? format(callbackDate, 'PPP') : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <CalendarComponent
                                                    mode="single"
                                                    selected={callbackDate}
                                                    onSelect={setCallbackDate}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>
                                    <div>
                                        <Label htmlFor="callback-time" className="block mb-1 text-sm font-medium">Time</Label>
                                        <Input
                                            id="callback-time"
                                            type="time"
                                            value={callbackTime}
                                            onChange={(e) => setCallbackTime(e.target.value)}
                                            className="h-10"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="callback-reason" className="block mb-1 text-sm font-medium">Reason for Callback</Label>
                                    <Input
                                        id="callback-reason"
                                        value={callbackReason}
                                        onChange={(e) => setCallbackReason(e.target.value)}
                                        placeholder="e.g., Follow up on pricing question"
                                        className="h-10"
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="callback-notes" className="block mb-1 text-sm font-medium">Notes</Label>
                                    <textarea
                                        id="callback-notes"
                                        value={notes}
                                        onChange={(e) => setNotes(e.target.value)}
                                        className="w-full p-2 text-sm border border-gray-300 rounded-md focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
                                        rows={3}
                                        placeholder="Add any additional notes for the callback..."
                                    />
                                </div>
                            </div>
                        </TabsContent>

                        {/* Next Action Tab */}
                        <TabsContent value="next-action" className="mt-4">
                            <div className="space-y-4">
                                <div>
                                    <Label htmlFor="next-action" className="block mb-1 text-sm font-medium">Select Next Action</Label>
                                    <Select value={nextAction} onValueChange={setNextAction}>
                                        <SelectTrigger className="w-full">
                                            <SelectValue placeholder="Select an action" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="email">Send Email</SelectItem>
                                            <SelectItem value="escalate">Escalate to Manager</SelectItem>
                                            <SelectItem value="ticket">Create Support Ticket</SelectItem>
                                            <SelectItem value="transfer">Transfer to Department</SelectItem>
                                            <SelectItem value="none">No Action Required</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {nextAction === 'email' && (
                                    <div>
                                        <Label htmlFor="email-template" className="block mb-1 text-sm font-medium">Email Template</Label>
                                        <Select>
                                            <SelectTrigger className="w-full">
                                                <SelectValue placeholder="Select a template" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="welcome">Welcome Email</SelectItem>
                                                <SelectItem value="followup">Follow-up</SelectItem>
                                                <SelectItem value="quote">Price Quote</SelectItem>
                                                <SelectItem value="support">Support Information</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                )}

                                {nextAction === 'escalate' && (
                                    <div>
                                        <Label htmlFor="escalate-reason" className="block mb-1 text-sm font-medium">Escalation Reason</Label>
                                        <Input
                                            id="escalate-reason"
                                            placeholder="Reason for escalation"
                                            className="h-10"
                                        />
                                    </div>
                                )}
                            </div>
                        </TabsContent>
                    </Tabs>

                    <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={handleClose} disabled={loading}>
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSubmit}
                            disabled={!selectedWorkCode || loading}
                            className="min-w-[100px]"
                        >
                            {loading ? 'Submitting...' : 'Submit'}
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
};

export default WorkCodeDrawer;
