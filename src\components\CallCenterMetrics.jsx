import { Card, CardContent, <PERSON>Header, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { 
  Users, 
  Phone, 
  PhoneIncoming, 
  PhoneOutgoing, 
  Clock, 
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Pause,
  PhoneMissed
} from 'lucide-react';

const MetricCard = ({ title, value, icon, description, trend, color = "blue", isLoading = false }) => {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-100",
    green: "text-green-600 bg-green-100",
    red: "text-red-600 bg-red-100",
    yellow: "text-yellow-600 bg-yellow-100",
    purple: "text-purple-600 bg-purple-100",
    orange: "text-orange-600 bg-orange-100",
  };

  const formatValue = (val) => {
    if (typeof val === 'number' && val > 999) {
      return (val / 1000).toFixed(1) + 'k';
    }
    return val;
  };

  const formatTime = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const displayValue = title.toLowerCase().includes('time') || title.toLowerCase().includes('wait') 
    ? formatTime(value) 
    : formatValue(value);

  if (isLoading) {
    return (
      <Card className="animate-pulse">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="h-8 bg-gray-200 rounded w-16"></div>
            </div>
            <div className="h-10 w-10 bg-gray-200 rounded-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <div className="flex items-center space-x-2">
              <p className="text-2xl font-bold text-gray-900">{displayValue}</p>
              {trend && (
                <Badge variant={trend > 0 ? "default" : "destructive"} className="text-xs">
                  {trend > 0 ? '+' : ''}{trend}%
                </Badge>
              )}
            </div>
            {description && (
              <p className="text-xs text-gray-500">{description}</p>
            )}
          </div>
          <div className={`p-2 rounded-full ${colorClasses[color]}`}>
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const CallCenterMetrics = ({ metrics, isLoading }) => {
  const metricsConfig = [
    {
      title: "Total Active Agents",
      value: metrics.totalActiveAgents || 0,
      icon: <Users className="h-5 w-5" />,
      color: "blue",
      description: "Currently logged in"
    },
    {
      title: "Available Agents",
      value: metrics.availableAgents || 0,
      icon: <CheckCircle className="h-5 w-5" />,
      color: "green",
      description: "Ready to take calls"
    },
    {
      title: "Busy Agents",
      value: metrics.busyAgents || 0,
      icon: <Phone className="h-5 w-5" />,
      color: "orange",
      description: "Currently on calls"
    },
    {
      title: "On Break",
      value: metrics.onBreakAgents || 0,
      icon: <Pause className="h-5 w-5" />,
      color: "yellow",
      description: "Taking a break"
    },
    {
      title: "Calls in Progress",
      value: metrics.totalCallsInProgress || 0,
      icon: <Phone className="h-5 w-5" />,
      color: "blue",
      description: "Active conversations"
    },
    {
      title: "Calls in Queue",
      value: metrics.callsInQueue || 0,
      icon: <AlertCircle className="h-5 w-5" />,
      color: "red",
      description: "Waiting to be answered"
    },
    {
      title: "Inbound Today",
      value: metrics.inboundCallsToday || 0,
      icon: <PhoneIncoming className="h-5 w-5" />,
      color: "green",
      description: "Customer initiated"
    },
    {
      title: "Outbound Today",
      value: metrics.outboundCallsToday || 0,
      icon: <PhoneOutgoing className="h-5 w-5" />,
      color: "purple",
      description: "Agent initiated"
    },
    {
      title: "Avg Wait Time",
      value: metrics.avgWaitTime || 0,
      icon: <Clock className="h-5 w-5" />,
      color: "yellow",
      description: "Before answer"
    },
    {
      title: "Avg Handle Time",
      value: metrics.avgHandleTime || 0,
      icon: <Clock className="h-5 w-5" />,
      color: "blue",
      description: "Call duration"
    },
    {
      title: "Service Level",
      value: `${metrics.serviceLevel || 0}%`,
      icon: <TrendingUp className="h-5 w-5" />,
      color: metrics.serviceLevel >= 90 ? "green" : metrics.serviceLevel >= 80 ? "yellow" : "red",
      description: "Calls answered in 30s"
    },
    {
      title: "Abandoned Calls",
      value: metrics.abandonedCalls || 0,
      icon: <PhoneMissed className="h-5 w-5" />,
      color: "red",
      description: "Hung up while waiting"
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {metricsConfig.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          icon={metric.icon}
          color={metric.color}
          description={metric.description}
          isLoading={isLoading}
        />
      ))}
    </div>
  );
};

export default CallCenterMetrics;
