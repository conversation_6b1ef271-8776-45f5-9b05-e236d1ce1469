import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { 
  Phone, 
  PhoneOutgoing, 
  PhoneIncoming, 
  PhoneMissed, 
  Clock, 
  Calendar,
  BarChart3,
  ChevronDown, 
  ChevronUp 
} from 'lucide-react';
import useAgentStore from '../lib/agentStore';

const KpiCard = ({ title, value, icon, description, trend, color = "blue" }) => {
  const colorClasses = {
    blue: "text-blue-600",
    green: "text-green-600",
    red: "text-red-600",
    yellow: "text-yellow-600",
    purple: "text-purple-600",
  };
  
  const bgColorClasses = {
    blue: "bg-blue-100",
    green: "bg-green-100",
    red: "bg-red-100",
    yellow: "bg-yellow-100",
    purple: "bg-purple-100",
  };
  
  return (
    <Card className="shadow-sm">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`mr-3 rounded-full p-2 ${bgColorClasses[color]}`}>
              {icon}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">{title}</p>
              <p className={`text-2xl font-bold ${colorClasses[color]}`}>{value}</p>
            </div>
          </div>
          
          {trend && (
            <Badge variant={trend.type === 'increase' ? 'success' : 'destructive'} className="text-xs">
              {trend.value}
            </Badge>
          )}
        </div>
        
        {description && (
          <p className="mt-2 text-xs text-gray-500">{description}</p>
        )}
      </CardContent>
    </Card>
  );
};

const AgentDashboard = () => {
  const { callHistory, status, statusDuration } = useAgentStore();
  const [expanded, setExpanded] = useState(true);
  const [kpiData, setKpiData] = useState({
    totalCalls: 0,
    inboundCalls: 0,
    outboundCalls: 0,
    missedCalls: 0,
    totalTalkTime: 0,
    avgTalkTime: 0,
    breakTime: 0
  });
  
  // Calculate KPIs from call history
  useEffect(() => {
    if (!callHistory.length) return;
    
    const today = new Date();
    const todayCalls = callHistory.filter(call => {
      const callDate = new Date(call.startTime);
      return (
        callDate.getDate() === today.getDate() &&
        callDate.getMonth() === today.getMonth() &&
        callDate.getFullYear() === today.getFullYear()
      );
    });
    
    const inboundCalls = todayCalls.filter(call => call.type === 'inbound');
    const outboundCalls = todayCalls.filter(call => call.type === 'outbound');
    const missedCalls = todayCalls.filter(call => call.type === 'missed');
    
    const totalTalkTime = todayCalls.reduce((sum, call) => sum + (call.duration || 0), 0);
    const avgTalkTime = todayCalls.length ? Math.round(totalTalkTime / todayCalls.length) : 0;
    
    setKpiData({
      totalCalls: todayCalls.length,
      inboundCalls: inboundCalls.length,
      outboundCalls: outboundCalls.length,
      missedCalls: missedCalls.length,
      totalTalkTime,
      avgTalkTime,
      breakTime: 0 // This would come from a break time tracker
    });
  }, [callHistory]);
  
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <Card className="shadow-sm">
      <CardHeader className="py-3 px-4 flex flex-row items-center justify-between">
        <CardTitle className="text-sm font-medium">Agent Performance</CardTitle>
        <Button variant="ghost" size="sm" onClick={() => setExpanded(!expanded)} className="h-8 w-8 p-0">
          {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </CardHeader>

      {expanded && (
        <CardContent className="px-4 pb-3">
          <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 lg:grid-cols-4">
            <KpiCard 
              title="Total Calls" 
              value={kpiData.totalCalls}
              icon={<Phone className="h-4 w-4 text-blue-600" />}
              color="blue"
            />
            
            <KpiCard 
              title="Inbound" 
              value={kpiData.inboundCalls}
              icon={<PhoneIncoming className="h-4 w-4 text-green-600" />}
              color="green"
            />
            
            <KpiCard 
              title="Outbound" 
              value={kpiData.outboundCalls}
              icon={<PhoneOutgoing className="h-4 w-4 text-purple-600" />}
              color="purple"
            />
            
            <KpiCard 
              title="Missed" 
              value={kpiData.missedCalls}
              icon={<PhoneMissed className="h-4 w-4 text-red-600" />}
              color="red"
            />
            
            <KpiCard 
              title="Talk Time" 
              value={formatDuration(kpiData.totalTalkTime)}
              icon={<Clock className="h-4 w-4 text-blue-600" />}
              color="blue"
            />
            
            <KpiCard 
              title="Avg Call" 
              value={formatDuration(kpiData.avgTalkTime)}
              icon={<BarChart3 className="h-4 w-4 text-yellow-600" />}
              color="yellow"
            />
            
            <KpiCard 
              title="Break Time" 
              value={formatDuration(kpiData.breakTime)}
              icon={<Calendar className="h-4 w-4 text-purple-600" />}
              color="purple"
            />
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default AgentDashboard;
