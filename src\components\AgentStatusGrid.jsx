import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback } from './ui/avatar';
import {
    Phone,
    Clock,
    CheckCircle,
    AlertCircle,
    Pause,
    PhoneOff,
    User
} from 'lucide-react';

const getStatusConfig = (status) => {
    const configs = {
        'Available': {
            color: 'bg-green-500',
            textColor: 'text-green-700',
            bgColor: 'bg-green-50',
            icon: <CheckCircle className="h-4 w-4" />,
            label: 'Available'
        },
        'On Call': {
            color: 'bg-blue-500',
            textColor: 'text-blue-700',
            bgColor: 'bg-blue-50',
            icon: <Phone className="h-4 w-4" />,
            label: 'On Call'
        },
        'Wrap-up': {
            color: 'bg-yellow-500',
            textColor: 'text-yellow-700',
            bgColor: 'bg-yellow-50',
            icon: <Clock className="h-4 w-4" />,
            label: 'Wrap-up'
        },
        'Break': {
            color: 'bg-orange-500',
            textColor: 'text-orange-700',
            bgColor: 'bg-orange-50',
            icon: <Pause className="h-4 w-4" />,
            label: 'Break'
        },
        'Not Ready': {
            color: 'bg-red-500',
            textColor: 'text-red-700',
            bgColor: 'bg-red-50',
            icon: <PhoneOff className="h-4 w-4" />,
            label: 'Not Ready'
        }
    };

    return configs[status] || configs['Not Ready'];
};

const formatDuration = (seconds) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes < 60) return `${minutes}m ${remainingSeconds}s`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
};

const AgentCard = ({ agent }) => {
    const statusConfig = getStatusConfig(agent.status);
    const initials = agent.name.split(' ').map(n => n[0]).join('').toUpperCase();

    return (
        <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
                <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                        <div className="relative">
                            <Avatar className="h-10 w-10">
                                <AvatarFallback className="text-sm font-medium">
                                    {initials}
                                </AvatarFallback>
                            </Avatar>
                            <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${statusConfig.color}`}></div>
                        </div>
                        <div>
                            <h3 className="font-medium text-gray-900">{agent.name}</h3>
                            <p className="text-sm text-gray-500">Ext. {agent.extension}</p>
                        </div>
                    </div>
                    <Badge
                        variant="secondary"
                        className={`${statusConfig.textColor} ${statusConfig.bgColor} border-0`}
                    >
                        <span className="flex items-center space-x-1">
                            {statusConfig.icon}
                            <span>{statusConfig.label}</span>
                        </span>
                    </Badge>
                </div>

                <div className="space-y-2">
                    {agent.status === 'On Call' && (
                        <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">Call Duration:</span>
                            <span className="font-medium text-blue-600">
                                {formatDuration(agent.currentCallDuration)}
                            </span>
                        </div>
                    )}

                    <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Calls Today:</span>
                        <span className="font-medium">{agent.callsToday}</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Avg Handle Time:</span>
                        <span className="font-medium">{formatDuration(agent.avgHandleTime)}</span>
                    </div>

                    <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Queues:</span>
                        <div className="flex flex-wrap gap-1">
                            {agent.queues.map((queue, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                    {queue}
                                </Badge>
                            ))}
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

const AgentStatusGrid = ({ agents, selectedQueue }) => {

    const statusCounts = agents.reduce((acc, agent) => {
        acc[agent.status] = (acc[agent.status] || 0) + 1;
        return acc;
    }, {});

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                        <User className="h-5 w-5" />
                        <span>Agent Status</span>
                        <Badge variant="outline">{agents.length} agents</Badge>
                    </CardTitle>

                    <div className="flex items-center space-x-2">
                        {Object.entries(statusCounts).map(([status, count]) => {
                            const config = getStatusConfig(status);
                            return (
                                <Badge
                                    key={status}
                                    variant="secondary"
                                    className={`${config.textColor} ${config.bgColor} border-0 text-xs`}
                                >
                                    {status}: {count}
                                </Badge>
                            );
                        })}
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                    {agents.map((agent) => (
                        <AgentCard key={agent.id} agent={agent} />
                    ))}
                </div>
            </CardContent>
        </Card>
    );
};

export default AgentStatusGrid;
