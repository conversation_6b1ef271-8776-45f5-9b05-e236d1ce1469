import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Button } from './ui/button';
import { LogOut, Menu, X } from 'lucide-react';
import StatusControls from './StatusControls';

const Header = () => {
    const { currentUser, logout } = useAuth();
    const navigate = useNavigate();
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    const handleLogout = async () => {
        try {
            await logout();
            navigate('/login');
        } catch (error) {
            console.error('Logout failed:', error);
        }
    };

    return (
        <header className="bg-white shadow">
            <div className="flex items-center justify-between p-4 mx-auto max-w-7xl">
                <div className="flex items-center">
                    <h1 className="text-xl font-bold text-gray-900">Agent Portal</h1>
                </div>

                <div className="hidden md:block">
                    <StatusControls />
                </div>

                <div className="items-center hidden md:flex">
                    {currentUser && (
                        <div className="mr-4 text-sm">
                            <span className="font-medium">
                                {currentUser.name || currentUser.username}
                            </span>
                        </div>
                    )}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleLogout}
                        className="flex items-center"
                    >
                        <LogOut className="w-4 h-4 mr-2" />
                        Logout
                    </Button>
                </div>

                <div className="md:hidden">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                    >
                        {mobileMenuOpen ? (
                            <X className="w-6 h-6" />
                        ) : (
                            <Menu className="w-6 h-6" />
                        )}
                    </Button>
                </div>
            </div>

            {mobileMenuOpen && (
                <div className="border-t border-gray-200 md:hidden">

                    <div className="px-4 py-3 border-b border-gray-200">
                        <StatusControls />
                    </div>

                    <div className="px-4 py-2 space-y-1">
                        {currentUser && (
                            <div className="py-2 text-sm font-medium">
                                {currentUser.name || currentUser.username}
                            </div>
                        )}
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleLogout}
                            className="flex items-center justify-center w-full"
                        >
                            <LogOut className="w-4 h-4 mr-2" />
                            Logout
                        </Button>
                    </div>
                </div>
            )}
        </header>
    );
};

export default Header;
