import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Badge } from '../components/ui/badge';
import {
    Phone,
    PhoneIncoming,
    PhoneOutgoing,
    Users,
    Clock,
    TrendingUp,
    AlertCircle,
    CheckCircle,
    Pause,
    Activity
} from 'lucide-react';
import CallCenterMetrics from '../components/CallCenterMetrics';
import AgentStatusGrid from '../components/AgentStatusGrid';
import CallQueueStatus from '../components/CallQueueStatus';
import RealTimeCharts from '../components/RealTimeCharts';
import CallDetailsTable from '../components/CallDetailsTable';
import useDashboardStore from '../lib/dashboardStore';

const LiveDashboardPage = () => {
    const [selectedQueue, setSelectedQueue] = useState('all');
    const [refreshInterval, setRefreshInterval] = useState(5000);

    const {
        metrics,
        agents,
        calls,
        queues,
        fetchDashboardData,
        initializeMockData
    } = useDashboardStore();

    useEffect(() => {
        initializeMockData();

        const interval = setInterval(() => {
            fetchDashboardData(selectedQueue);
        }, refreshInterval);

        return () => clearInterval(interval);
    }, [selectedQueue, refreshInterval, fetchDashboardData, initializeMockData]);

    const filteredMetrics = selectedQueue === 'all' ? metrics : metrics;
    const filteredAgents = selectedQueue === 'all'
        ? agents
        : agents.filter(agent => agent.queues.includes(selectedQueue));
    const filteredCalls = selectedQueue === 'all'
        ? calls
        : calls.filter(call => call.queue === selectedQueue);

    return (
        <div className="min-h-screen bg-gray-50 p-4">
            <div className="mb-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Live Call Center Dashboard</h1>
                        <p className="text-gray-600 text-sm">Real-time monitoring and analytics</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-700">Queue:</span>
                            <Select value={selectedQueue} onValueChange={setSelectedQueue}>
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder="Select queue" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Queues</SelectItem>
                                    {queues.map(queue => (
                                        <SelectItem key={queue.id} value={queue.id}>
                                            {queue.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Activity className="h-4 w-4 text-green-500 animate-pulse" />
                            <span className="text-sm text-gray-600">Live</span>
                        </div>
                    </div>
                </div>
            </div>

            <CallCenterMetrics metrics={filteredMetrics} />

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mt-4">
                <div className="lg:col-span-1">
                    <CallQueueStatus queues={selectedQueue === 'all' ? queues : queues.filter(q => q.id === selectedQueue)} />
                </div>
                <div className="lg:col-span-2">
                    <RealTimeCharts metrics={filteredMetrics} selectedQueue={selectedQueue} />
                </div>
            </div>

            <div className="mt-4">
                <AgentStatusGrid agents={filteredAgents} selectedQueue={selectedQueue} />
            </div>

            <div className="mt-4 mb-8">
                <CallDetailsTable calls={filteredCalls} agents={filteredAgents} />
            </div>
        </div>
    );
};

export default LiveDashboardPage;
