import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from './ui/card';
import { ScrollArea } from './ui/scroll-area';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { But<PERSON> } from './ui/button';

// Sample script data - in a real app, this would come from an API or store
const sampleScripts = [
    {
        id: 1,
        title: 'Greeting Script',
        content: 'Thank you for calling our service. My name is [Your Name], how may I assist you today?'
    },
    {
        id: 2,
        title: 'Problem Resolution',
        content: "I understand you're experiencing an issue with [Problem]. Let me help you resolve that. First, could you please provide me with [Required Information]?"
    },
    {
        id: 3,
        title: 'Service Offering',
        content: 'We offer several solutions that might meet your needs. Our most popular options are [Option 1], [Option 2], and [Option 3]. Would you like me to explain any of these in more detail?'
    },
    {
        id: 4,
        title: 'Closing Script',
        content: "Thank you for contacting us today. Is there anything else I can help you with? [Wait for response] It's been a pleasure assisting you. Have a great day!"
    },
    {
        id: 5,
        title: 'Handling Objections',
        content: 'I understand your concern about [Objection]. Many customers have felt the same way initially. However, [Address Objection with Benefit]. Would that help address your concern?'
    }
];

const ScriptWidget = () => {
    const [activeScript, setActiveScript] = useState(0);
    const [isExpanded, setIsExpanded] = useState(true);

    const handleNextScript = () => {
        if (activeScript < sampleScripts.length - 1) {
            setActiveScript(activeScript + 1);
        }
    };

    const handlePreviousScript = () => {
        if (activeScript > 0) {
            setActiveScript(activeScript - 1);
        }
    };

    const toggleExpand = () => {
        setIsExpanded(!isExpanded);
    };

    return (
        <Card className="shadow-sm">
            <CardHeader className="py-3 px-4 flex flex-row items-center justify-between">
                <CardTitle className="text-sm font-medium">Agent Script</CardTitle>
                <Button variant="ghost" size="sm" onClick={toggleExpand} className="h-8 w-8 p-0">
                    {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
            </CardHeader>

            {isExpanded && (
                <CardContent className="px-4 pb-3">
                    <div className="mb-2 flex items-center justify-between">
                        <span className="text-xs font-semibold text-gray-500">
                            {activeScript + 1}/{sampleScripts.length}: {sampleScripts[activeScript].title}
                        </span>
                        <div className="flex space-x-1">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handlePreviousScript}
                                disabled={activeScript === 0}
                                className="h-6 w-6 p-0"
                            >
                                <ChevronUp className="h-3 w-3" />
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleNextScript}
                                disabled={activeScript === sampleScripts.length - 1}
                                className="h-6 w-6 p-0"
                            >
                                <ChevronDown className="h-3 w-3" />
                            </Button>
                        </div>
                    </div>

                    <ScrollArea className="h-24 rounded-md border p-2 bg-gray-50">
                        <p className="text-sm">{sampleScripts[activeScript].content}</p>
                    </ScrollArea>
                </CardContent>
            )}
        </Card>
    );
};

export default ScriptWidget;
