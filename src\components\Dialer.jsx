import { useState, useEffect, useRef } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import useAgentStore from '../lib/agentStore';
import sipService from '../lib/sipService';
import { Mic, MicOff, PhoneOff, Phone, Pause, Play, UserPlus, X } from 'lucide-react';
import IncomingCallModal from './IncomingCallModal';

const Dialer = ({ onCallEnded }) => {
    const [phoneNumber, setPhoneNumber] = useState('');
    const [callStatus, setCallStatus] = useState('idle');
    const [sipInitialized, setSipInitialized] = useState(false);
    const [error, setError] = useState('');
    const [isMuted, setIsMuted] = useState(false);
    const [isHold, setIsHold] = useState(false);
    const [callDuration, setCallDuration] = useState(0);
    const [isTransferring, setIsTransferring] = useState(false);
    const [transferNumber, setTransferNumber] = useState('');
    const [showIncomingModal, setShowIncomingModal] = useState(false);
    const [incomingNumber, setIncomingNumber] = useState('');
    const [pendingCallData, setPendingCallData] = useState(null);
    const timerRef = useRef(null);

    const { status, setCallData, clearCallData, setNotReady, setReady } = useAgentStore();

    useEffect(() => {
        let mounted = true;

        const initializeSip = async () => {
            try {
                console.log('Setting up SIP callbacks...');

                sipService.setCallbacks({
                    onCallReceived: (invitation) => {
                        if (!mounted) return;
                        console.log('Call received', invitation);
                        const callerId = invitation.remoteIdentity.uri.user || 'Unknown';

                        const newCallData = {
                            id: Date.now().toString(),
                            type: 'inbound',
                            phoneNumber: callerId,
                            startTime: new Date(),
                        };

                        setPendingCallData(newCallData);
                        setIncomingNumber(callerId);
                        setShowIncomingModal(true);
                    },
                    onCallConnected: () => {
                        if (!mounted) return;
                        console.log('Call connected');
                        setCallStatus('connected');
                    },
                    onCallEnded: (reason) => {
                        if (!mounted) return;
                        console.log('Call ended, reason:', reason);

                        setCallStatus('idle');
                        setCallDuration(0);
                        setIsMuted(false);
                        setIsHold(false);
                        setPhoneNumber('');

                        clearCallData();

                        if (reason === 'remote' || reason === 'terminated') {
                            console.log('Call was ended by the remote party');
                            setError('Call ended by the other party');

                            setNotReady('After Call Work').then(() => {
                                const callEndedData = {
                                    id: Date.now().toString(),
                                    type: 'ended_by_remote',
                                    endTime: new Date(),
                                };

                                if (typeof onCallEnded === 'function') {
                                    onCallEnded(callEndedData);
                                }
                            });
                        }
                    },
                    onRegistered: () => {
                        if (!mounted) return;
                        console.log('SIP registered');
                        setSipInitialized(true);

                        if (status !== 'READY' && status !== 'ON_CALL') {
                            console.log('Updating agent status to READY after SIP registration');
                            setReady();
                        }
                    },
                    onUnregistered: () => {
                        if (!mounted) return;
                        console.log('SIP unregistered');
                        setSipInitialized(false);

                        if (status === 'READY') {
                            console.log('Updating agent status to NOT_READY after SIP unregistration');
                            setNotReady('SIP disconnected');
                        }
                    },
                    onRegistrationFailed: (error) => {
                        if (!mounted) return;
                        console.error('SIP registration failed', error);
                        setError('SIP registration failed: ' + (error?.message || 'Unknown error'));
                        setSipInitialized(false);
                    },
                });

                console.log('Initializing SIP service...');

                const initialized = await sipService.initialize();

                if (initialized && mounted) {
                    console.log('SIP service initialized successfully');
                    setSipInitialized(true);
                    setError('');
                    console.log('SIP service ready - waiting for user to set Ready status');
                } else if (mounted) {
                    console.error('Failed to initialize SIP service');
                    setError('Failed to initialize SIP service - check console for details');
                }
            } catch (error) {
                if (mounted) {
                    console.error('SIP initialization error:', error);
                    setError('Failed to initialize SIP service: ' + (error?.message || 'Unknown error'));
                }
            }
        };

        initializeSip();

        return () => {
            console.log('Dialer component unmounting...');
            mounted = false;
        };
    }, [clearCallData, status, setReady, setNotReady]);

    useEffect(() => {
        if (callStatus === 'connected' && !isHold) {
            timerRef.current = setInterval(() => {
                setCallDuration(prev => prev + 1);
            }, 1000);
        } else if (timerRef.current && (callStatus !== 'connected' || isHold)) {
            clearInterval(timerRef.current);
        }

        return () => {
            if (timerRef.current) {
                clearInterval(timerRef.current);
            }
        };
    }, [callStatus, isHold]);
    const formatDuration = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const handleAcceptCall = async () => {
        if (!pendingCallData) return;

        try {
            console.log('Accepting incoming call');
            await sipService.answerCall();

            setCallStatus('connected');
            setCallDuration(0);
            setShowIncomingModal(false);
            setCallData(pendingCallData);
        } catch (error) {
            console.error('Error answering call:', error);
            setError('Answer failed: ' + (error?.message || 'Unknown error'));
            setShowIncomingModal(false);
            setPendingCallData(null);
        }
    };

    const handleRejectCall = async () => {
        try {
            console.log('Rejecting incoming call');
            await sipService.rejectCall();

            setShowIncomingModal(false);
            setPendingCallData(null);
            setIncomingNumber('');
        } catch (error) {
            console.error('Error rejecting call:', error);
            setShowIncomingModal(false);
            setPendingCallData(null);
        }
    };
    const handleCall = async () => {
        if (!phoneNumber.trim()) return;

        try {
            setError('');
            setCallStatus('calling');
            setCallDuration(0);
            console.log(`Making call to ${phoneNumber}`);

            const callTimeout = setTimeout(() => {
                if (callStatus === 'calling') {
                    console.warn('Call establishment is taking longer than expected');
                    setError('Call is taking longer than expected. Please wait...');
                }
            }, 5000);

            const success = await sipService.makeCall(phoneNumber);

            clearTimeout(callTimeout);

            if (success) {
                setCallData({
                    id: Date.now().toString(),
                    type: 'outbound',
                    phoneNumber,
                    startTime: new Date(),
                });

                setTimeout(() => {
                    if (callStatus === 'calling') {
                        console.warn('Call did not connect within expected time');
                        setError('Call connection is delayed. You may continue to wait or try again.');
                    }
                }, 10000);
            } else {
                setCallStatus('idle');
                setError('Call failed - could not establish connection. Please check the number and try again.');
            }
        } catch (error) {
            console.error('Error making call:', error);
            setCallStatus('idle');
            setError('Call failed: ' + (error?.message || 'Unknown error'));
        }
    };
    const handleHangup = async () => {
        try {
            console.log('Hanging up call');

            const currentCallData = {
                id: Date.now().toString(),
                type: 'outbound',
                endTime: new Date(),
            };

            setCallStatus('idle');
            setCallDuration(0);
            setIsMuted(false);
            setIsHold(false);
            setPhoneNumber('');

            clearCallData();

            const success = await sipService.hangupCall();

            if (success) {
                console.log('Call successfully hung up');
                setError('Call ended successfully');
                setTimeout(() => setError(''), 3000);
            } else {
                console.warn('SIP hangup call may have failed, but UI has been reset');
            }

            await setNotReady('After Call Work');

            if (typeof onCallEnded === 'function') {
                onCallEnded(currentCallData);
            }
        } catch (error) {
            console.error('Error hanging up call:', error);
            setError('Hangup failed: ' + (error?.message || 'Unknown error'));

            setCallStatus('idle');
            setCallDuration(0);
            setIsMuted(false);
            setIsHold(false);
            setPhoneNumber('');
        }
    };
    const handleAnswer = async () => {
        try {
            await new Promise(resolve => setTimeout(resolve, 800));

            setCallStatus('connected');
            setCallDuration(0);
        } catch (error) {
            console.error('Error answering call (simulated):', error);
            setError('Answer failed');
        }
    };

    const handleToggleMute = async () => {
        try {
            const newMuteState = !isMuted;
            if (newMuteState) {
                await sipService.mute();
            } else {
                await sipService.unmute();
            }
            setIsMuted(newMuteState);
        } catch (error) {
            console.error('Error toggling mute:', error);
            setError('Failed to ' + (isMuted ? 'unmute' : 'mute') + ' call');
        }
    };

    const handleToggleHold = async () => {
        try {
            const newHoldState = !isHold;
            if (newHoldState) {
                await sipService.holdCall();
                setCallStatus('hold');
            } else {
                await sipService.resumeCall();
                setCallStatus('connected');
            }
            setIsHold(newHoldState);
        } catch (error) {
            console.error('Error toggling hold:', error);
            setError('Failed to ' + (isHold ? 'resume' : 'hold') + ' call');
        }
    };
    const handleStartTransfer = () => {
        setIsTransferring(true);
        setTransferNumber('');
        if (!isHold) {
            setIsHold(true);
        }
    };

    const handleCancelTransfer = () => {
        setIsTransferring(false);
        setTransferNumber('');
    };

    const handleCompleteTransfer = async () => {
        if (!transferNumber.trim()) return;

        try {
            console.log(`Transferring call to ${transferNumber}`);

            const success = await sipService.transferCall(transferNumber);

            if (success) {
                setCallStatus('idle');
                setCallDuration(0);
                setIsMuted(false);
                setIsHold(false);
                setIsTransferring(false);
                setTransferNumber('');
                clearCallData();

                setError('Call successfully transferred');
                setTimeout(() => setError(''), 3000);
            } else {
                setError('Transfer failed - could not complete transfer');
            }
        } catch (error) {
            console.error('Error transferring call:', error);
            setError('Transfer failed: ' + (error?.message || 'Unknown error'));
        }
    };
    const handleDtmf = async (digit) => {
        const dtmfButton = document.getElementById(`dtmf-${digit}`);
        if (dtmfButton) {
            dtmfButton.classList.add('bg-primary/20');
            setTimeout(() => {
                dtmfButton.classList.remove('bg-primary/20');
            }, 200);
        }

        if (callStatus === 'idle') {
            setPhoneNumber(prev => prev + digit);
        } else if (isTransferring) {
            setTransferNumber(prev => prev + digit);
        } else if (callStatus === 'connected') {
            try {
                console.log(`Sending DTMF: ${digit}`);
                await sipService.sendDTMF(digit);
            } catch (error) {
                console.error('Error sending DTMF tone:', error);
            }
        }
    };

    const isDialerDisabled = status === 'OFFLINE' || status === 'BREAK' || !sipInitialized;
    const getDisabledMessage = () => {
        if (status === 'BREAK') {
            return 'Dialer is disabled while on break';
        } else if (status === 'OFFLINE') {
            return 'Dialer is disabled while offline';
        } else if (!sipInitialized) {
            return 'Initializing phone system...';
        }
        return '';
    };

    return (
        <div className="border rounded-lg shadow-sm">
            <div className="p-3">
                <h3 className="mb-2 text-sm font-medium">Dialer</h3>

                <IncomingCallModal
                    isVisible={showIncomingModal}
                    phoneNumber={incomingNumber}
                    onAccept={handleAcceptCall}
                    onReject={handleRejectCall}
                />

                {error && (
                    <div className="p-1.5 mb-2 text-xs text-red-700 rounded-md bg-red-50">
                        {error}
                    </div>
                )}

                {isDialerDisabled && (
                    <div className="p-1.5 mb-2 text-xs text-gray-700 bg-gray-100 rounded-md">
                        {getDisabledMessage()}
                    </div>
                )}

                <div className="mb-2">
                    <Input
                        type="tel"
                        placeholder="Enter phone number"
                        value={phoneNumber}
                        onChange={(e) => setPhoneNumber(e.target.value)}
                        disabled={callStatus !== 'idle' || isDialerDisabled}
                        className="h-8 text-sm"
                    />
                </div>
                {callStatus !== 'idle' && (
                    <div className="flex items-center justify-between p-1.5 mb-2 bg-gray-100 rounded-md">
                        <div>
                            <p className="text-xs font-medium">
                                {callStatus === 'calling' && 'Calling...'}
                                {callStatus === 'ringing' && 'Incoming call...'}
                                {callStatus === 'connected' && 'Call in progress'}
                                {callStatus === 'hold' && 'Call on hold'}
                            </p>
                            {(callStatus === 'connected' || callStatus === 'hold') && (
                                <p className="text-xs text-gray-500">Duration: {formatDuration(callDuration)}</p>
                            )}
                        </div>
                        {(callStatus === 'connected' || callStatus === 'hold') && (
                            <div className="flex space-x-1">
                                <Button
                                    size="sm"
                                    variant="ghost"
                                    className={`h-6 w-6 p-0 ${isMuted ? "text-red-500" : ""}`}
                                    onClick={handleToggleMute}
                                    title="Mute/Unmute"
                                >
                                    {isMuted ? <MicOff size={14} /> : <Mic size={14} />}
                                </Button>
                                <Button
                                    size="sm"
                                    variant="ghost"
                                    className={`h-6 w-6 p-0 ${isHold ? "text-amber-500" : ""}`}
                                    onClick={handleToggleHold}
                                    title="Hold/Resume"
                                >
                                    {isHold ? <Play size={14} /> : <Pause size={14} />}
                                </Button>
                                <Button
                                    size="sm"
                                    variant="ghost"
                                    className={`h-6 w-6 p-0 ${isTransferring ? "text-blue-500" : ""}`}
                                    onClick={handleStartTransfer}
                                    title="Transfer Call"
                                >
                                    <UserPlus size={14} />
                                </Button>
                            </div>
                        )}
                    </div>
                )}

                {isTransferring && (
                    <div className="p-2 mb-2 border border-blue-200 rounded-md bg-blue-50">
                        <div className="flex items-center justify-between mb-1">
                            <h4 className="text-xs font-medium text-blue-700">Transfer Call</h4>
                            <Button
                                size="sm"
                                variant="ghost"
                                className="w-5 h-5 p-0 text-gray-500"
                                onClick={handleCancelTransfer}
                            >
                                <X size={14} />
                            </Button>
                        </div>
                        <div className="mb-1">
                            <Input
                                type="tel"
                                placeholder="Enter transfer number"
                                value={transferNumber}
                                onChange={(e) => setTransferNumber(e.target.value)}
                                className="text-sm border-blue-200 h-7"
                            />
                        </div>
                        <Button
                            size="sm"
                            className="w-full text-xs h-7"
                            onClick={handleCompleteTransfer}
                            disabled={!transferNumber.trim()}
                        >
                            Complete Transfer
                        </Button>
                    </div>
                )}
                <div className="grid grid-cols-3 gap-1 mb-2">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, '*', 0, '#'].map((digit) => (
                        <Button
                            key={digit}
                            id={`dtmf-${digit}`}
                            variant="outline"
                            size="sm"
                            onClick={() => handleDtmf(digit)}
                            disabled={isDialerDisabled && callStatus === 'idle'}
                            className="h-8 p-0 text-sm transition-colors"
                        >
                            {digit}
                        </Button>
                    ))}
                </div>

                <div className="flex space-x-2">
                    {callStatus === 'idle' && (
                        <Button
                            onClick={handleCall}
                            disabled={!phoneNumber.trim() || isDialerDisabled}
                            className="flex items-center justify-center w-full h-8 text-xs"
                            size="sm"
                        >
                            <Phone className="w-3 h-3 mr-1" /> Call
                        </Button>
                    )}

                    {callStatus === 'ringing' && (
                        <Button
                            onClick={handleAnswer}
                            className="flex items-center justify-center w-full h-8 text-xs bg-green-600 hover:bg-green-700"
                            size="sm"
                        >
                            <Phone className="w-3 h-3 mr-1" /> Answer
                        </Button>
                    )}

                    {(callStatus === 'calling' || callStatus === 'connected' || callStatus === 'hold') && (
                        <Button
                            onClick={handleHangup}
                            variant="destructive"
                            className="flex items-center justify-center w-full h-8 text-xs"
                            size="sm"
                        >
                            <PhoneOff className="w-3 h-3 mr-1" /> Hang Up
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Dialer;
